package cn.mioffice.id.service.api;

import cn.mioffice.id.IdTest;
import cn.mioffice.id.dao.entity.AccountDo;
import cn.mioffice.id.dao.entity.UserDo;
import cn.mioffice.id.dto.input.QueryDto;
import cn.mioffice.id.dto.output.PageDto;
import cn.mioffice.id.dto.output.UserBaseInfoDto;
import cn.mioffice.id.service.base.UserService;
import cn.mioffice.id.web.controller.api.ApiUserController;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;


/**
 * ApiUserService 对外提供用户信息相关服务的服务类
 *
 * <AUTHOR>
 * @Data 2020/2/4 上午11:32
 */
public class ApiUserServiceTest extends IdTest {

    @Autowired
    private ApiUserService apiUserService;

    @Autowired
    private ApiUserController apiUserController;

    @Autowired
    private UserService userService;

    /**
     * 功能描述: 通过uId查询用户全量信息
     */
    @Test
    public void findUserByUid() {
        UserDo userdo = apiUserService.findUserByUid("e688138341d843ee901bf7bc98c6365d");
        Assert.assertNotNull(userdo);
    }

    /**
     * 功能描述: 通过uId查询用户基本信息
     */
    @Test
    public void findUserBaseInfoByUid() {
        UserBaseInfoDto userBaseInfoDto = apiUserService.findUserBaseInfoByUid("e7be87beee8942a39c5c67151939a160");
        Assert.assertNotNull(userBaseInfoDto);
    }

    /**
     * 功能描述: 通过员工工号查询uid
     */
    @Test
    public void findUidByPersonId() {
        String uid = apiUserService.findUidByPersonId("26896");
        Assert.assertNotNull(uid);
    }

    /**
     * 功能描述: 通过uid为查询上级uid
     * 原uic的 /rs/usernotify/findSuperiour 接口
     */
    @Test
    public void findSuperiorUidByUid() {
//        String uid = apiUserService.findSuperiorByUid("44692a0de97f497b88dd87dbec691ca6");
//        assert StringUtils.isNotBlank(uid);
    }

    /**
     * 功能描述: 通过员工卡卡号查询uid
     * 原 /rs/usernotify/findEmployeeNoByCardNumber 接口
     */
    @Test
    public void findUidByCardNo() {
        String uid = apiUserService.findUidByCardNo("2ffcc4f0");
        assert StringUtils.isNotBlank(uid);
    }

    /**
     * 功能描述: 根据米聊号获取uid
     */
    @Test
    public void findUidByMiliao() {
        String uid = apiUserService.findUidByMiliao("2202525374");
        assert StringUtils.isNotBlank(uid);
    }


    /**
     * 功能描述: 根据uid获取米聊号
     */
    @Test
    public void findMiliaoByUid() {
        String miliao = apiUserService.findMiliaoByUid("fe94b26e61ad4683a02f04721152566f");
        System.out.println(miliao);
//        assert StringUtils.isNotBlank(miliao);
    }

    /**
     * 功能描述: 根据uid获取用户手偏好语言
     */
    @Test
    public void findLanguageByUid() {
        apiUserService.findLanguageByUid("2202525374");
    }


    /**
     * 功能描述: 根据用户类型询用户列表
     */
    @Test
    public void findHireUserListByType() {
        PageDto<UserDo> page = apiUserService.findHireUserListByType("employee", 1, 1);
        assert page != null && CollectionUtils.isNotEmpty(page.getRecords());
    }

    /**
     * 功能描述: 通过在职状态查询所有米聊号不为空的用户信息（userName，miliao,员工号）
     */
    @Test
    public void findMiIdsByHrStatus() {
        PageDto<String> page = apiUserService.findMiIdsByHrStatus("A", 1, 10);
        assert page != null && CollectionUtils.isNotEmpty(page.getRecords());
    }

    /**
     * 功能描述: 查询所有在职员工信息（username type email displayName name）
     */
    @Test
    public void findAllHireUser() {
        PageDto<UserDo> page = apiUserService.findAllHireUser(1, 10);
        assert page != null && CollectionUtils.isNotEmpty(page.getRecords());
    }

    /**
     * 功能描述: 查询所有在职员工的米聊号
     */
    @Test
    public void findHireMiIds() {
        PageDto<String> page = apiUserService.findHireMiIds(1, 10);
        assert page != null && CollectionUtils.isNotEmpty(page.getRecords());
    }


    /**
     * 功能描述：分页查询在职员工信息
     */
    @Test
    public void findUserPage() {
        PageDto<UserDo> page = apiUserService.findUserPage(1, 10);
        System.out.println(page);
//        assert page != null && CollectionUtils.isNotEmpty(page.getRecords());
    }

    /**
     * 功能描述：根据入职时间查询用户
     */
    @Test
    public void findEmployeesByHireDate() {
        LocalDate now = LocalDate.now();
        PageDto<UserDo> page = apiUserService.findUsersByHireDate(now.minusYears(1), now, 1, 10);
        assert page != null && CollectionUtils.isNotEmpty(page.getRecords());
    }

    /**
     * 功能描述：根据query查询用户
     */
    @Test
    public void findUsersByQuery() {
        QueryDto query = new QueryDto();
        query.setDeptId("IT");
        PageDto<UserDo> page = apiUserService.findUsersByQuery(query, 1, 10);
        assert page != null && CollectionUtils.isNotEmpty(page.getRecords());
    }

    @Test
    public void findAvatarByUid() {
//        String miliao = apiUserService.findAvatarByUid("44692a0de97f497b88dd87dbec691ca6");
        System.out.println(apiUserController.findAvatarByUid("46d3f122598745818ff32b342094db3b"));
    }


    @Test
    public void updateMobileByUserName()   {
//        String miliao = apiUserService.findAvatarByUid("44692a0de97f497b88dd87dbec691ca6");
        apiUserService.updateMobileByUserName("zhangyuhang", "+89", "11111",false, true);
    }

    @Test
    public void saveAccount()   {
//        String miliao = apiUserService.findAvatarByUid("44692a0de97f497b88dd87dbec691ca6");
        AccountDo accountDo = AccountDo.builder().state("2").email("<EMAIL>").userName("perry01").uid("46d3f122598745818ff32b342204db3b").build();
        apiUserService.saveOrUpdateEmployee(null,accountDo);
    }

//    /**
//     * 通过用户名模糊查询用户
//     */
//    @Test
//    public void fuzzyFindUserListByUserName() {
//        List<UserDo> userDos = account.fuzzyFindUserListByUserName("lupeng", 10);
//        assert CollectionUtils.isNotEmpty(userDos);
//    }
//
//    /**
//     * 通过uid查询手机号
//     */
//    @Test
//    public void findMobileByUid() {
//        String mobile = apiUserService.findMobileByUid("44692a0de97f497b88dd87dbec691ca6");
//        assert StringUtils.isNotBlank(mobile);
//    }
//
//    /**
//     * 通过手机号查询用户uid
//     */
//    @Test
//    public void findUidByMobile() {
//        String uid = apiUserService.findUidByMobile("***********");
//        assert StringUtils.isNotBlank(uid);
//    }
//
//    /**
//     * 通过用户名 类型 在职状态查询用户uid
//     */
//    @Test
//    public void findUidByUserName() {
//        String uid = apiUserService.findUidByUserName("lupeng6", "employee", "A");
//        assert StringUtils.isNotBlank(uid);
//    }
//
//    /**
//     * 通过邮箱查询 uid
//     */
//    @Test
//    public void findUidByEmail() {
//        String uid = apiUserService.findUidByEmail("<EMAIL>");
//        assert StringUtils.isNotBlank(uid);
//    }
//
//    /**
//     * 通过uid list 查询用户
//     */
//    @Test
//    public void findUserListByUids() {
//        List<UserDo> userDos = apiUserService.findUserListByUids(Lists.newArrayList("44692a0de97f497b88dd87dbec691ca6"));
//        assert CollectionUtils.isNotEmpty(userDos);
//    }
//
//    /**
//     * 通过uid查询账号信息
//     */
//    @Test
//    public void findUserSimpleInfoByUid() {
//        UserDo userdo = apiUserService.findUserSimpleInfoByUid("44692a0de97f497b88dd87dbec691ca6");
//        Assert.assertNotNull(userdo);
//    }


    @Test
    public void testQueryListPage() {
        List<Long> longs = userService.listActiveId();

        List<String> strings = userService.listActiveMiliao();

        System.out.println(longs.size());
    }
}
