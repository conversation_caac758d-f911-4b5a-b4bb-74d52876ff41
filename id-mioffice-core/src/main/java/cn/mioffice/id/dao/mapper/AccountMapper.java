package cn.mioffice.id.dao.mapper;

import cn.mioffice.id.dao.entity.AccountDo;
import cn.mioffice.id.dto.output.es.ESAccountDto;
import cn.mioffice.id.dto.output.oakbay.OakbayAccountDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @version 1.0
 * @className AccountMapper
 * @description:
 * @author: <PERSON><PERSON><PERSON>
 * @date: 2020/3/5 下午4:37
 **/
public interface AccountMapper extends BaseMapper<AccountDo> {
    List<OakbayAccountDto> listAllEsAccount(@Param("lastId") long lastId, @Param("limit") int limit);

    List<String> listAllRemoveIndex(@Param("lastId") long lastId, @Param("limit") int limit);

    List<ESAccountDto> listAllEsAccountV2(@Param("lastId") long lastId, @Param("limit") int limit);
}
