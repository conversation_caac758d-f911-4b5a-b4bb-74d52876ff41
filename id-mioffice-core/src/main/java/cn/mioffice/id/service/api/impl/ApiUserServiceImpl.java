package cn.mioffice.id.service.api.impl;

import cn.mioffice.id.common.config.notify.Notify;
import cn.mioffice.id.common.config.properties.AesProperties;
import cn.mioffice.id.common.config.properties.NotifyProperties;
import cn.mioffice.id.common.exceptions.BusinessErrorEnum;
import cn.mioffice.id.common.exceptions.BusinessException;
import cn.mioffice.id.common.util.AesUtils;
import cn.mioffice.id.common.util.ThreadPoolUtil;
import cn.mioffice.id.dao.entity.*;
import cn.mioffice.id.dao.enums.UserHrStatus;
import cn.mioffice.id.dao.enums.UserType;
import cn.mioffice.id.dto.input.QueryDto;
import cn.mioffice.id.dto.output.PageDto;
import cn.mioffice.id.dto.output.UserBaseInfoDto;
import cn.mioffice.id.idmng.AccountRemote;
import cn.mioffice.id.service.api.ApiUserService;
import cn.mioffice.id.service.base.AccountService;
import cn.mioffice.id.service.base.DepartmentService;
import cn.mioffice.id.service.base.UserCardService;
import cn.mioffice.id.service.base.UserService;
import cn.mioffice.id.service.cache.RedisBloomFilterService;
import cn.mioffice.id.service.cache.RefreshCacheDto;
import cn.mioffice.id.service.cache.RefreshTypeEnum;
import cn.mioffice.id.service.cache.impl.AccountCacheServiceImpl;
import cn.mioffice.id.service.cache.impl.DepartmentCacheServiceImpl;
import cn.mioffice.id.service.cache.impl.UserCacheServiceImpl;
import cn.mioffice.id.service.cache.impl.UserSuperiorCacheServiceImpl;
import cn.mioffice.id.service.external.NotifyService;
import cn.mioffice.id.service.external.PsInfoService;
import cn.mioffice.id.service.generate.EmployeeEntryService;
import cn.mioffice.id.service.generate.impl.GenerateUicAccountServiceImpl;
import cn.mioffice.it.rest.sdk.dto.input.EntryDto;
import cn.mioffice.it.rest.sdk.dto.output.EntryResponse;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Stopwatch;
import com.google.i18n.phonenumbers.NumberParseException;
import com.google.i18n.phonenumbers.PhoneNumberUtil;
import com.google.i18n.phonenumbers.Phonenumber;
import com.it.itp.model.UserModel;
import com.mi.info.idm.service.rep.AccountInfoDto;
import com.mi.info.idm.service.req.UpdateAccountDto;
import com.mi.oa.infra.oaucf.core.exception.CommonErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.internal.CommonErrors;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.mioffice.id.common.constants.CommonCst.AVAILABLE_ACCOUNT_STATE;
import static cn.mioffice.id.common.constants.CommonCst.UNAVAILABLE_ACCOUNT_STATE;
import static cn.mioffice.id.common.constants.RedisPrefixCst.ADD_IDM_ZSET;

/**
 * UserNotifyServiceImpl 对外提供用户相关服务的服务类
 *
 * <AUTHOR>
 * @Data 2020/2/4 上午11:32
 */
@Slf4j
@Service
public class ApiUserServiceImpl implements ApiUserService {
    @Autowired
    private UserService userService;

    @Autowired
    private AccountService accountService;
    @Autowired
    private UserSuperiorCacheServiceImpl userSuperiorCacheService;
    @Autowired
    private UserCardService userCardService;
    @Autowired
    private UserCacheServiceImpl userCacheService;
    @Autowired
    private AccountCacheServiceImpl accountCacheService;
    @Autowired
    private AesProperties aesProperties;
    @Autowired
    private RedisBloomFilterService redisBloomFilterService;
    @Autowired
    private PsInfoService psInfoService;
    @Autowired
    private ApiUserService apiUserService;
    @Autowired
    private EmployeeEntryService employeeEntryService;
    @Autowired
    private NotifyService notifyService;
    @Autowired
    private NotifyProperties notifyProperties;
    @Autowired
    private AccountRemote accountRemote;
    @Autowired
    private DepartmentService departmentService;
    @Autowired
    private DepartmentCacheServiceImpl departmentCacheService;
    @Autowired
    private GenerateUicAccountServiceImpl generateUicAccountServiceImpl;

    @Override
    public UserDo findUserByUid(String uid) {
        return userCacheService.getByUid(uid);
    }

    @Override
    public UserBaseInfoDto findUserBaseInfoByUid(String uid) {
        Stopwatch startedStopwatch = Stopwatch.createStarted();
        UserDo user = userCacheService.getByUid(uid);
        log.info("userCacheService.findUserBaseInfoByUid time {}",startedStopwatch.elapsed(TimeUnit.MILLISECONDS));
        Stopwatch startedStopwatch2 = Stopwatch.createStarted();
        if (user != null) {
            UserBaseInfoDto baseInfo = new UserBaseInfoDto();
            BeanUtils.copyProperties(user, baseInfo);
            log.info("ApiUserServiceImpl.findUserBaseInfoByUid other time {}",startedStopwatch2.elapsed(TimeUnit.MILLISECONDS));
            return baseInfo;
        }
        return null;
    }


    @Override
    public String findUidByPersonId(String personId) {
        UserDo userDo = userCacheService.getUserByPersonId(personId);
        if (userDo != null) {
            return userDo.getUid();
        }
        return null;
    }

    @Override
    public UserDo findSuperiorByUid(String uid) {
        if (StringUtils.isBlank(uid)) {
            return null;
        }
        UserDo user = userCacheService.getByUid(uid);
        if (Objects.nonNull(user) && "partner".equals(user.getType())) {
            AccountInfoDto account = accountRemote.getAccount(user.getUserName());
            if (Objects.isNull(account) || Objects.isNull(account.getAccountOwner())) {
               return null;
            }
            return userCacheService.getByUserName(account.getAccountOwner());
        }
        if (user != null && StringUtils.isNotBlank(user.getOriginalPersonId())) {
            UserSuperiorDo superiorUser = userSuperiorCacheService.getByOriginalPersonId(user.getOriginalPersonId());
            if (superiorUser != null && StringUtils.isNotBlank(superiorUser.getSuperiorPersonId())) {
                String superiorPersonId = superiorUser.getSuperiorPersonId();
                UserDo userDo = userCacheService.getUserByPersonId(superiorPersonId);
                if (userDo != null) {
                    return userDo;
                }
            }
        }
        return null;
    }

    @Override
    public String findSuperiorUidByUid(String uid) {
        UserDo userDo = findSuperiorByUid(uid);
        if (userDo != null) {
            return userDo.getUid();
        }
        return null;
    }

    @Override
    public String findUidByCardNo(String cardNo) {
        UserCardDo userCard = userCardService.getUserCardByPhysicalOrEncrypted(cardNo);
        if (userCard != null) {
            return userCard.getUid();
        }
        return null;
    }

    @Override
    public String findUidByMiliao(String miliao) {
        UserDo userDo = userCacheService.getUserByMiliao(miliao);
        /**由于该接口QPS很高，严禁直接查询数据库*/
        /*if (userDo == null) {
            userDo = userService.getUserByMiliao(miliao);
        }*/
        if (userDo != null) {
            return userDo.getUid();
        }
        return null;
    }

    @Override
    public String findMiliaoByUid(String uid) {
        UserDo user = userCacheService.getByUid(uid);
        if (user != null) {
            return user.getMiliao();
        }
        AccountDo account = accountCacheService.getByUid(uid);
        if (account != null) {
            return account.getMiliao();
        }
        return null;
    }


    @Override
    public String findLanguageByUid(String uid) {
        UserDo user = userCacheService.getByUid(uid);
        if (user != null) {
            return user.getDefaultLanguage();
        }
        return null;
    }


    @Override
    public PageDto<UserDo> findHireUserListByType(String type, Integer currentPage, Integer pageSize) {
        Page<UserDo> page = new Page<>();
        page.setCurrent(currentPage).setSize(pageSize);
        Page<UserDo> userPage = userService.getHireUserByType(type, page);
        PageDto<UserDo> resultPage = new PageDto<>();
        List<UserDo> list = userPage.getRecords();
        list.forEach(r -> r.setPassport(null).setMobile(null).setZoneCode(null).setUpdateUser(null).setUpdateTime(null).setCreateTime(null).setId(null));
        resultPage.setCurrentPage(currentPage).setPageSize(pageSize).setRecords(list).setTotal((int) userPage.getTotal());
        return resultPage;
    }

    @Override
    public PageDto<String> findMiIdsByHrStatus(String hrStatus, Integer currentPage, Integer pageSize) {
        if (StringUtils.isBlank(hrStatus)) {
            hrStatus = UserHrStatus.ACTIVE.getId();
        }
        Page<UserDo> page = new Page<>();
        page.setCurrent(currentPage).setSize(pageSize);
        Page<UserDo> userPage = userService.getUsersMiliao(hrStatus, page);
        List<UserDo> records = userPage.getRecords();

        List<String> miLiao = null;
        if (CollectionUtils.isNotEmpty(records)) {
            miLiao = records.stream().map(UserDo::getMiliao).collect(Collectors.toList());
        }
        long aLong = userPage.getTotal();
        PageDto<String> retPage = new PageDto<>();
        retPage.setCurrentPage(currentPage);
        retPage.setPageSize(pageSize);
        retPage.setTotal((int) aLong);
        retPage.setRecords(miLiao);

        return retPage;
    }

    /**
     * 功能描述: 查询所有在职员工信息（uid,username type email displayName name）
     *
     * @param currentPage
     * @param pageSize
     * @return
     * @Author: Perry_Zhu
     * @Date: 2020/2/4 上午11:32
     */
    @Override
    public PageDto<UserDo> findAllHireUser(Integer currentPage, Integer pageSize) {
        Page<UserDo> page = new Page<>();
        page.setCurrent(currentPage).setSize(pageSize);
        List<UserDo> list = userCacheService.listHireUserByPage(currentPage, pageSize);
        int totalSize = userCacheService.getTotalSize();
        List<UserDo> records = null;
        if (CollectionUtils.isNotEmpty(list)) {
            records = list.parallelStream().map(u -> UserDo.builder().uid(u.getUid()).personId(u.getPersonId()).originalPersonId(u.getOriginalPersonId()).userName(u.getUserName()).name(u.getName()).email(u.getEmail()).displayName(u.getDisplayName()).type(u.getType()).build()).collect(Collectors.toList());
        }
        PageDto<UserDo> retPage = new PageDto<>();
        retPage.setCurrentPage(currentPage);
        retPage.setPageSize(pageSize);
        retPage.setTotal(totalSize);
        retPage.setRecords(records);
        return retPage;

    }

    @Override
    public PageDto<String> findHireMiIds(Integer currentPage, Integer pageSize) {
        PageDto<String> retPage = new PageDto<>();
        retPage.setRecords(userCacheService.listHireMiIds(currentPage, pageSize));
        retPage.setCurrentPage(currentPage);
        retPage.setPageSize(pageSize);
        retPage.setTotal(userCacheService.getMiliaoSize());
        return retPage;

    }


    @Override
    public PageDto<UserDo> findUserPage(int currentPage, int pageSize) {
        PageDto<UserDo> resultPage = new PageDto<>();
        List<UserDo> list = userCacheService.listHireUserByPage(currentPage, pageSize);
        resultPage.setTotal(userCacheService.getTotalSize());
        resultPage.setCurrentPage(currentPage).setPageSize(pageSize).setRecords(list);
        return resultPage;
    }

    /**
     * 通过入职时间查询员工
     *
     * @param startDate
     * @param endDate
     * @param currentPage
     * @param pageSize
     * @return cn.mioffice.uic.dto.output.PageDto<cn.mioffice.uic.dao.entity.UserDo>
     * <AUTHOR>
     * @date 2020/3/24 18:50
     */
    @Override
    public PageDto<UserDo> findUsersByHireDate(LocalDate startDate, LocalDate endDate, Integer currentPage, Integer pageSize) {
        Page<UserDo> page = new Page<>();
        page.setCurrent(currentPage).setSize(pageSize);
        Page<UserDo> userPage = userService.getUsersByHireDate(startDate, endDate, page);
        return setUserPageDto(userPage, currentPage, pageSize);
    }

    @Override
    public PageDto<UserDo> findUsersPageByUpdateTime(String hrStatus, LocalDateTime startDate, LocalDateTime endDate, Integer currentPage, Integer pageSize) {
        Page<UserDo> page = new Page<>();
        page.setCurrent(currentPage).setSize(pageSize);
        Page<UserDo> userPage = userService.findUsersPageByUpdateTime(null, hrStatus, startDate, endDate, page);
        return setUserPageDto(userPage, currentPage, pageSize);
    }

    @Override
    public PageDto<UserDo> findUsersByQuery(QueryDto query, Integer currentPage, Integer pageSize) {
        Page<UserDo> page = new Page<>();
        page.setCurrent(currentPage).setSize(pageSize);
        Page<UserDo> userPage = userService.getUsersByQuery(query, page);
        return setUserPageDto(userPage, currentPage, pageSize);

    }

    private PageDto<UserDo> setUserPageDto(Page<UserDo> userPage, Integer currentPage, Integer pageSize) {
        PageDto<UserDo> pageDto = new PageDto<>();
        pageDto.setPageSize(pageSize);
        pageDto.setCurrentPage(currentPage);
        if (userPage != null) {
            List<UserDo> records = userPage.getRecords();
            if (CollectionUtils.isNotEmpty(records)) {
                for (UserDo u : records) {
                    u.setPassport(null).setMobile(null).setZoneCode(null).setUpdateUser(null).setUpdateTime(null).setCreateTime(null).setId(null);
                    AccountDo accountDo = accountCacheService.getByUid(u.getUid());
                    if (accountDo != null) {
                        u.setHeadUrl(accountDo.getHeadUrl()).setThumbnailUrl(accountDo.getThumbnailUrl());
                    }
                }
            }
            long aLong = userPage.getTotal();
            pageDto.setTotal((int) aLong);
            pageDto.setRecords(records);
        }
        return pageDto;
    }

    @Override
    public List<UserDo> findUidsByUserNames(List<String> userNameList) {
        return userCacheService.listUidByUserNames(userNameList);
    }

    @Override
    public List<UserDo> findUidListByPersonIds(List<String> personIdList) {
        return userCacheService.listUidByPersonIdList(personIdList);
    }

    @Override
    public UserBaseInfoDto findUserBaseInfoByMiliao(String miliao) {
        UserDo user = userCacheService.getUserByMiliao(miliao);
        if (user != null) {
            return UserBaseInfoDto.builder()
                    .uid(user.getUid())
                    .personId(user.getPersonId())
                    .originalPersonId(user.getOriginalPersonId())
                    .name(user.getName())
                    .displayName(user.getDisplayName())
                    .type(user.getType())
                    .userName(user.getUserName())
                    .email(user.getEmail())
                    .sex(user.getSex())
                    .deptId(user.getDeptId())
                    .deptDescr(user.getDeptDescr())
                    .fullDeptDescr(user.getFullDeptDescr())
                    .company(user.getCompany())
                    .companyDescr(user.getCompanyDescr())
                    .hrStatus(user.getHrStatus())
                    .build();
        }
        return null;
    }

    @Override
    public List<UserBaseInfoDto> findUserBaseInfoListByMiliaos(List<String> miliaoList) {
        List<UserDo> userDos = userCacheService.listBaseInfoListByMiliaos(miliaoList);

        if (CollectionUtils.isNotEmpty(userDos)) {
            return userDos.parallelStream().map(user -> UserBaseInfoDto.builder()
                    .uid(user.getUid())
                    .personId(user.getPersonId())
                    .originalPersonId(user.getOriginalPersonId())
                    .name(user.getName())
                    .displayName(user.getDisplayName())
                    .type(user.getType())
                    .miliao(user.getMiliao())
                    .userName(user.getUserName())
                    .email(user.getEmail())
                    .sex(user.getSex())
                    .deptId(user.getDeptId())
                    .deptDescr(user.getDeptDescr())
                    .fullDeptDescr(user.getFullDeptDescr())
                    .company(user.getCompany())
                    .companyDescr(user.getCompanyDescr())
                    .hrStatus(user.getHrStatus())
                    .build()).collect(Collectors.toList());
        }

        return null;
    }

    @Override
    public void updateDeptIdByAccounts(List<String> accountList, String deptId, Boolean isMiWork) {

        DepartmentDo byDeptId = departmentService.getByDeptId(deptId);
        if (Objects.isNull(byDeptId)) {
            throw new BusinessException(BusinessErrorEnum.DATA_NOT_FIND, "deptId未找到: " + deptId);
        }

        List<UserDo> userDos = userService.queryByUserNames(accountList);

        for (UserDo userDo : userDos) {
            if (StringUtils.startsWith(byDeptId.getDeptParallel(), "MI-IT")) {
                // 限定MIT，同步飞书部门
                userDo.setDeptId(deptId);
                userDo.setDeptDescr(byDeptId.getDeptName());
                userDo.setFullDeptDescr(byDeptId.getDeptFullDescr());
                if (BooleanUtils.isTrue(isMiWork)) {
                    userDo.setIsMiWork(Boolean.TRUE);
                }
            } else {
                // 如果离开限定部门，则走老逻辑，复用管理员的部门
                userDo.setIsMiWork(Boolean.FALSE);
            }
        }

        generateUicAccountServiceImpl.batchSaveOrUpdate(null, null, userDos, null);
    }

    @Override
    public void updateUserDepartment(String userName, String deptId, boolean isMiWork) {
        UserDo userDo = userCacheService.getByUserName(userName);
        if (Objects.isNull(userDo) || !userDo.getHrStatus().equals(UserHrStatus.ACTIVE.getId())) {
            log.info("用户离职，未处理，当前用户为：{}", userName);
            return;
        }
        DepartmentDo departmentDo = departmentCacheService.getByDeptid(deptId);
        if (Objects.isNull(departmentDo) || departmentDo.getStatus().equals("I")) {
            log.info("部门不存在，未处理，当前部门为：{}", deptId);
            return;
        }

        log.info("update user department,username:{}, deptId:{}", userName, deptId);

        UserDo updateUser = new UserDo();
        updateUser.setId(userDo.getId());
        updateUser.setDeptId(deptId);
        updateUser.setDeptDescr(departmentDo.getDeptName());
        updateUser.setFullDeptDescr(departmentDo.getDeptFullDescr());
        updateUser.setIsMiWork(isMiWork);

        userService.updateById(updateUser);
        userCacheService.refresh(new RefreshCacheDto<UserDo>(updateUser.getId(), UserDo::getUserName, RefreshTypeEnum.ADD, "2000"));
    }

    /**
     * 根据userNames 批量查询米聊号
     *
     * @param userNameList
     * <AUTHOR>
     * @date 2020/9/8 16:40
     */
    @Override
    public List<UserDo> findMiliaosByUserNames(List<String> userNameList) {
        if (CollectionUtils.isNotEmpty(userNameList)) {
            return userCacheService.listMiliaoByUserNames(userNameList);
        }

        return Collections.emptyList();
    }

    @Override
    public PageDto<UserDo> listPreEntryUserByCreateTime(LocalDateTime startTime, LocalDateTime endTime, Integer currentPage, Integer pageSize) {
        Page<UserDo> page = new Page<>();
        page.setCurrent(currentPage).setSize(pageSize);
        Page<UserDo> userPage = userService.listPreEntryUserByCreateTime(startTime, endTime, page);
        PageDto<UserDo> pageDto = new PageDto<>();
        pageDto.setPageSize(pageSize);
        pageDto.setCurrentPage(currentPage);
        long aLong = userPage.getTotal();
        pageDto.setTotal((int) aLong);
        pageDto.setRecords(userPage.getRecords());
        return pageDto;
    }

    /**
     * 根据userName  修改手机号
     *
     * @param userName 账号名
     * @param mobile   未加密手机号
     * @return
     * <AUTHOR>
     * @date 2020/6/17 5:53 下午
     */
    @Override
    @Notify
    public void updateMobileByUserName(String userName, String zoneCode, String mobile, boolean notifyPs, boolean updateIdm) {
        if (StringUtils.isBlank(zoneCode) || mobile.startsWith("+")) {
            try {
                Phonenumber.PhoneNumber numberProto = PhoneNumberUtil.getInstance().parse(mobile, "");
                zoneCode = "+" + numberProto.getCountryCode();
                if (mobile.startsWith(zoneCode)) {
                    mobile = mobile.substring(zoneCode.length());
                } else {
                    mobile = numberProto.getNationalNumber() + "";
                }
            } catch (NumberParseException e) {
                log.error("IDM变更手机号异常：无法转换国家码，userName=" + userName + ";变更后手机号=" + mobile);
                zoneCode = "+86";
                if (mobile.startsWith("+")) {
                    mobile = mobile.substring(1);
                }
            }
        }
        if (StringUtils.isNotBlank(zoneCode) && !zoneCode.startsWith("+")) {
            zoneCode = "+" + zoneCode;
        }
        UserDo user = userCacheService.getByUserName(userName);
        AccountDo account = accountCacheService.getByUserName(userName);
        log.info("{} 更新手机号为 {},{}", userName, zoneCode, mobile);
        if (account != null) {
            String aesMobile = AesUtils.encrypt(mobile, aesProperties.getUicKey());
            apiUserService.updateAllMobileByUserName(userName, zoneCode, aesMobile);
            if (updateIdm) {
                UpdateAccountDto updateAccountDto = new UpdateAccountDto();
                updateAccountDto.setAccountName(account.getUserName());
                updateAccountDto.setMobile(mobile);
                updateAccountDto.setZoneCode(zoneCode);
                accountRemote.updateAccount(account.getUserName(), updateAccountDto);
            }
            // 刷新缓存
            AccountDo accountDo = accountService.getAccountByUserName(userName);
            UserDo userDo = userService.getUserByUserName(userName);
            if (accountDo != null && accountDo.getId() != null) {
                log.info("IDM变更手机号缓存信息:accountId={},zone={},mobile={}", accountDo.getId(), zoneCode, aesMobile);
                accountCacheService.refresh(new RefreshCacheDto<AccountDo>(accountDo.getId(), AccountDo::getZoneCode, RefreshTypeEnum.UPDATE, zoneCode));
                accountCacheService.refresh(new RefreshCacheDto<AccountDo>(accountDo.getId(), AccountDo::getMobile, RefreshTypeEnum.UPDATE, aesMobile));
            }
            if (userDo != null && userDo.getId() != null) {
                log.info("IDM变更手机号缓存信息:userId={},zone={},mobile={}", userDo.getId(), zoneCode, aesMobile);
                userCacheService.refresh(new RefreshCacheDto<UserDo>(userDo.getId(), UserDo::getZoneCode, RefreshTypeEnum.UPDATE, zoneCode));
                userCacheService.refresh(new RefreshCacheDto<UserDo>(userDo.getId(), UserDo::getMobile, RefreshTypeEnum.UPDATE, aesMobile));
            }

            // 生产环境通知PS变更手机号，否则不变更
            if (notifyPs && "PS".equalsIgnoreCase(account.getOperateChannel()) && UserType.getPsUserTypes().contains(account.getType())) {
                String finalPhone = zoneCode + mobile;
                ThreadPoolUtil.getCommonPoll().submit(() -> {
                    psInfoService.updateInfo(user.getPersonId(), user.getName(), user.getCountry(), finalPhone, "", "", "");
                });
            }
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAllMobileByUserName(String userName, String zoneCode, String mobile) {
        if (StringUtils.isNotBlank(userName) && StringUtils.isNotBlank(zoneCode) && StringUtils.isNotBlank(mobile)) {
            userService.updateMobileByUserName(userName, zoneCode, mobile);
            accountService.updateMobileByUserName(userName, zoneCode, mobile);
        }
    }

    /**
     * 功能描述: 修改米聊号
     *
     * @param userName
     * @param miliao
     * @Return: void
     * @Author: zhangyuhang
     * @Date: 2020/6/18 下午3:14
     */
    @Override
    @Notify
    public boolean updateMiliaoByUserName(String userName, String miliao) {
        boolean b = updateMiliao(userName, miliao);
        UpdateAccountDto updateAccountDto = new UpdateAccountDto();
        updateAccountDto.setAccountName(userName);
        updateAccountDto.setMiliao(miliao);
        accountRemote.updateAccount(userName, updateAccountDto);
        if (b) {
            log.info("{} 的 miliao号：{} 更新成功", userName, miliao);
            //将更新的miliao加入布隆过滤器
            redisBloomFilterService.addByBloomFilter("miliaoFilter", miliao);
            // 刷新缓存
            AccountDo accountDo = accountCacheService.getByUserName(userName);
            UserDo userDo = userCacheService.getByUserName(userName);
            if (accountDo != null && accountDo.getId() != null) {
                accountCacheService.refresh(new RefreshCacheDto<AccountDo>(accountDo.getId(), AccountDo::getMiliao, RefreshTypeEnum.UPDATE, miliao));
            }
            if (userDo != null && userDo.getId() != null) {
                userCacheService.refresh(new RefreshCacheDto<UserDo>(userDo.getId(), UserDo::getMiliao, RefreshTypeEnum.UPDATE, miliao));
            }
            //发送notify
            Map<String, Object> notifyParamMap = new HashMap<>(4);
            notifyParamMap.put("userName", accountDo.getUserName());
            notifyParamMap.put("uid", accountDo.getUid());
            notifyParamMap.put("miliao", miliao);
            notifyParamMap.put("versionId", System.currentTimeMillis());
            try {
                notifyService.sendIdmNotify(notifyParamMap, notifyProperties.getUpdateMiliaoProject());
            } catch (IOException e) {
                log.error("IDM发送notify异常,米聊号变更异常:" + e.getMessage(), e);
            }
            return true;
        } else {
            throw new BusinessException(BusinessErrorEnum.UPDATE_ERROR);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMiliao(String userName, String miliao) {
        LambdaUpdateWrapper<UserDo> userUpdateWrapper = Wrappers.<UserDo>lambdaUpdate()
                .set(UserDo::getMiliao, miliao)
                .eq(UserDo::getUserName, userName);
        boolean update = userService.update(userUpdateWrapper);

        LambdaUpdateWrapper<AccountDo> accountUpdateWrapper = Wrappers.<AccountDo>lambdaUpdate()
                .set(AccountDo::getMiliao, miliao)
                .eq(AccountDo::getUserName, userName);
        boolean update1 = accountService.update(accountUpdateWrapper);
        return update && update1;
    }

    /**
     * 功能描述: 修改语言
     *
     * @param userName
     * @param language
     * @Return: void
     * @Author: zhangyuhang
     * @Date: 2020/6/18 下午3:14
     */
    @Override
    @Notify
    @Transactional(rollbackFor = Exception.class)
    public void updateLanguageByUserName(String userName, String language) {
        LambdaUpdateWrapper<UserDo> userUpdateWrapper = Wrappers.<UserDo>lambdaUpdate()
                .set(UserDo::getDefaultLanguage, language)
                .eq(UserDo::getUserName, userName);
        userService.update(userUpdateWrapper);
        // 刷新缓存
        UserDo userDo = userCacheService.getByUserName(userName);
        if (userDo != null && userDo.getId() != null) {
            userCacheService.refresh(new RefreshCacheDto<UserDo>(userDo.getId(), UserDo::getDefaultLanguage, RefreshTypeEnum.UPDATE, language));
        }
    }


    /**
     * 功能描述: 修改语言
     *
     * @param personId
     * @param language
     * @Return: void
     * @Author: zhangyuhang
     * @Date: 2020/6/18 下午3:14
     */
    @Override
    @Notify
    @Transactional(rollbackFor = Exception.class)
    public void updateLanguageByPersonId(String personId, String language) {
        LambdaUpdateWrapper<UserDo> userUpdateWrapper = Wrappers.<UserDo>lambdaUpdate()
                .set(UserDo::getDefaultLanguage, language)
                .eq(UserDo::getPersonId, personId);
        userService.update(userUpdateWrapper);
        // 刷新缓存
        UserDo userDo = userCacheService.getUserByPersonId(personId);
        if (userDo != null) {
            userCacheService.refresh(new RefreshCacheDto<UserDo>(userDo.getId(), UserDo::getDefaultLanguage, RefreshTypeEnum.UPDATE, language));
        }
    }


    @Override
    public List<UserDo> listUserListByUids(List<String> uidList) {
        return userCacheService.listUserByUids(uidList);
    }

    /**
     * 根据uid 更新淘宝账号
     *
     * @param uid
     * @param taoBao
     * <AUTHOR>
     * @date 2020/8/28 19:16
     */
    @Override
    public void updateTaoBaoByUid(String uid, String taoBao) {
        if (StringUtils.isNotBlank(uid) && StringUtils.isNotBlank(taoBao)) {
            UserDo user = userService.getUserByUid(uid);
            if (user != null && user.getId() != null) {
                userService.updateById(UserDo.builder().taobao(taoBao).id(user.getId()).build());
                userCacheService.refresh(new RefreshCacheDto<UserDo>(user.getId(), UserDo::getTaobao, RefreshTypeEnum.UPDATE, taoBao));
            }
        }
    }

    /**
     * 根据uid 更新衣服尺码
     *
     * @param uid
     * @param clothesSize
     * <AUTHOR>
     * @date 2020/8/28 19:21
     */
    @Override
    public void updateClothesSize(String uid, String clothesSize) {
        if (StringUtils.isNotBlank(uid) && StringUtils.isNotBlank(clothesSize)) {
            UserDo user = userService.getUserByUid(uid);
            if (user != null && user.getId() != null) {
                userService.updateById(UserDo.builder().clothingSize(clothesSize).id(user.getId()).build());
                userCacheService.refresh(new RefreshCacheDto<UserDo>(user.getId(), UserDo::getClothingSize, RefreshTypeEnum.UPDATE, clothesSize));
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateEmployee(UserDo user, AccountDo account) {
        if (account != null) {
            if (account.getId() != null) {
                accountService.updateById(account);
                accountCacheService.updateRefreshByT(account);
            } else {
                accountService.save(account);
                accountCacheService.refresh(new RefreshCacheDto<AccountDo>(account.getId(), AccountDo::getUserName, RefreshTypeEnum.ADD, "2000"));
            }
        }
        if (user != null) {
            if (user.getId() != null) {
                userService.updateById(user);
                userCacheService.updateRefreshByT(user);
            } else {
                userService.save(user);
                userCacheService.refresh(new RefreshCacheDto<UserDo>(user.getId(), UserDo::getUserName, RefreshTypeEnum.ADD, "2000"));
            }
        }
    }

}
