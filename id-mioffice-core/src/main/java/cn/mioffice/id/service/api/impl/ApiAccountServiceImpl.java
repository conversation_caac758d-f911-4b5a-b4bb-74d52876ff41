package cn.mioffice.id.service.api.impl;

import cn.mioffice.id.common.config.properties.AesProperties;
import cn.mioffice.id.common.util.AesUtils;
import cn.mioffice.id.common.util.Assert;
import cn.mioffice.id.common.util.ThreadPoolUtil;
import cn.mioffice.id.dao.entity.AccountDo;
import cn.mioffice.id.dao.entity.UserDo;
import cn.mioffice.id.dto.output.AccountDto;
import cn.mioffice.id.dto.output.AccountOwnerDto;
import cn.mioffice.id.dto.output.MobileComboDto;
import cn.mioffice.id.dto.output.PageDto;
import cn.mioffice.id.dto.output.oakbay.OakbayAccountDto;
import cn.mioffice.id.idmng.AccountRemote;
import cn.mioffice.id.service.api.ApiAccountService;
import cn.mioffice.id.service.base.AccountService;
import cn.mioffice.id.service.base.DepartmentService;
import cn.mioffice.id.service.cache.RefreshCacheDto;
import cn.mioffice.id.service.cache.RefreshTypeEnum;
import cn.mioffice.id.service.cache.impl.AccountCacheServiceImpl;
import cn.mioffice.id.service.cache.impl.UserCacheServiceImpl;
import cn.mioffice.id.service.external.EsUserService;
import cn.mioffice.id.service.external.FeiShuService;
import cn.mioffice.id.service.external.OakbayUserService;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Stopwatch;
import com.google.i18n.phonenumbers.NumberParseException;
import com.google.i18n.phonenumbers.PhoneNumberUtil;
import com.google.i18n.phonenumbers.Phonenumber;
import com.mi.info.idm.service.rep.AccountInfoDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.mioffice.id.common.constants.CommonCst.AVAILABLE_ACCOUNT_STATE;

/**
 * @version 1.0
 * @className ApiAccountServiceImpl
 * @description: 账户服务类
 * @author: Perry_Zhu
 * @date: 2020/4/29 上午9:45
 **/
@Slf4j
@Service
public class ApiAccountServiceImpl implements ApiAccountService {

    @Autowired
    private AccountCacheServiceImpl accountCacheService;
    @Autowired
    private AesProperties aesProperties;
    @Autowired
    private AccountService accountService;
    @Autowired
    private OakbayUserService oakbayUserService;
    @Autowired
    private EsUserService esUserService;
    @Value("${api.avatar.url.prefix}")
    private String avatarUrlPrefix;

    @Autowired
    private DepartmentService departmentService;

    @Autowired
    private AccountRemote accountRemote;

    @Autowired
    private UserCacheServiceImpl userCacheService;

    @NacosValue(value = "${esSwitch.enableES}", autoRefreshed = true)
    private boolean enableES;

    @NacosValue(value = "${esSwitch.enableOakbay}", autoRefreshed = true)
    private boolean enableOakbay;

    @NacosValue(value = "${esSwitch.returnData}", autoRefreshed = true)
    private String returnData;

    @Autowired
    private FeiShuService feiShuService;


    @Override
    public AccountDto findAccountByUid(String uid) {
        if (StringUtils.isNotBlank(uid)) {
            AccountDo account = accountCacheService.getByUid(uid);
            if (account != null) {
                AccountDto accountDo = new AccountDto();

                if (StringUtils.equals(account.getFreeze(), "Y")) {
                    log.info("账号冻结通过partner系统查询解冻连接userName={}", account.getUserName());
                    String accountUid = account.getUid();
                    //String unFreezeUrl = getUnFreezeUrlByPartner(accountUid);
                    //accountDo.setUnFreezeUrl(unFreezeUrl);
                }
                accountDo
                        .setUserName(account.getUserName())
                        .setHeadUrl(account.getHeadUrl())
                        .setThumbnailUrl(account.getThumbnailUrl())
                        .setUid(account.getUid())
                        .setDisplayName(account.getDisplayName())
                        .setType(account.getType())
                        .setEmail(account.getEmail())
                        .setFreeze(account.getFreeze())
                        .setState(account.getState())
                        .setLastLoginTime(account.getLastLoginTime());
                return accountDo;
            }
        }
        return null;
    }

    /**
     * 通过uid查询手机号
     *
     * @param uid
     * @return java.lang.String
     * <AUTHOR>
     * @date 2020/4/229 11:15
     */
    @Override
    public String findMobileByUid(String uid) {
        String mobile = null;
        if (StringUtils.isNotBlank(uid)) {
            AccountDo account = accountCacheService.getByUid(uid);
            if (account != null && StringUtils.isNotBlank(account.getMobile())) {
                mobile = AesUtils.decrypt(account.getMobile(), aesProperties.getUicKey());
            }
        }
        return mobile;
    }

    @Override
    public String findAvatarByUid(String uid) {
        if (StringUtils.isNotBlank(uid)) {
            AccountDo account = accountCacheService.getByUid(uid);
            if (account != null) {
                return account.getThumbnailUrl();
            }
        }

        return null;
    }

    /**
     * @return cn.mioffice.id.dto.output.MobileComboDto
     * @描述:
     * @Param [uid]
     * <AUTHOR>
     * @Date 2020/8/6 7:40 下午
     */

    @Override
    public MobileComboDto findFullMobileByUid(String uid, Boolean isNew) {
        if (StringUtils.isBlank(uid)) {
            return null;
        }
        MobileComboDto mobileCombo = new MobileComboDto();

        AccountDo account = accountCacheService.getByUid(uid);
        if (account != null && StringUtils.isNotBlank(account.getMobile()) && StringUtils.isNotBlank(account.getZoneCode())) {
            mobileCombo.setMobile(AesUtils.decrypt(account.getMobile(), aesProperties.getUicKey())).setZoneCode(account.getZoneCode());
        } else {
            log.warn("get findFullMobileByUid empty, uid: {}", uid);
        }
        if (StringUtils.isBlank(mobileCombo.getMobile())) {
            AccountDo accountInDb = accountService.getAccountByUid(uid);
            if (accountInDb != null && StringUtils.isNotBlank(accountInDb.getMobile()) && StringUtils.isNotBlank(accountInDb.getZoneCode())) {
                mobileCombo.setMobile(AesUtils.decrypt(accountInDb.getMobile(), aesProperties.getUicKey())).setZoneCode(accountInDb.getZoneCode());
                accountCacheService.refresh(new RefreshCacheDto<>(accountInDb.getId(), AccountDo::getMobile, RefreshTypeEnum.ADD, "2000"));
            }
        }
        return mobileCombo;
    }

    /**
     * 通过手机号查询用户uid
     *
     * @param mobile
     * @return java.lang.String
     * <AUTHOR>
     * @date 2020/4/229 11:15
     */
    @Override
    public String findUidByMobile(String mobile) {
        String uid = null;
        if (StringUtils.isNotBlank(mobile)) {
            try {
                Phonenumber.PhoneNumber numberProto = PhoneNumberUtil.getInstance().parse(mobile, "");
                String zoneCode = "+" + numberProto.getCountryCode();
                if (mobile.startsWith(zoneCode)) {
                    mobile = mobile.substring((zoneCode).length());
                } else {
                    mobile = numberProto.getNationalNumber() + "";
                }
            } catch (NumberParseException e) {

            }
            List<AccountDo> accountDos = accountCacheService.getListByField(AccountDo::getMobile, AesUtils.encrypt(mobile, aesProperties.getUicKey()));
            if (CollectionUtils.isNotEmpty(accountDos)) {
                List<AccountDo> list = accountDos.stream().filter(a -> AVAILABLE_ACCOUNT_STATE.equals(a.getState())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(list)) {
                    uid = list.get(0).getUid();
                }
            }
        }
        return uid;
    }

    @Override
    public List<AccountDo> findAccountListByFullMobile(String fullMobile) {
        if (StringUtils.isNotBlank(fullMobile)) {
            String mobile = null;
            //员工大部分为国内手机号，做特殊处理 ，可以避免转换手机号
            if (fullMobile.startsWith("+86")) {
                mobile = fullMobile.substring(3);
            } else {
                try {
                    Phonenumber.PhoneNumber numberProto = PhoneNumberUtil.getInstance().parse(fullMobile, "");
                    String zoneCode = "+" + numberProto.getCountryCode();
                    if (fullMobile.startsWith(zoneCode)) {
                        mobile = fullMobile.substring(zoneCode.length());
                    } else {
                        mobile = numberProto.getNationalNumber() + "";
                    }
                } catch (NumberParseException e) {
                    log.error(e.getMessage(),e);
                }
            }
            if (StringUtils.isNotBlank(mobile)) {
                return findAccountListByMobile(mobile);
            }
        }
        return null;
    }

    @Override
    public List<AccountDo> findAccountListByMobile(String mobile) {
        if (StringUtils.isNotBlank(mobile)) {
            List<AccountDo> list = accountCacheService.listAccountByMobile(mobile);
            if (CollectionUtils.isNotEmpty(list)) {
                List<AccountDo> resultList = new ArrayList<>(list.size());
                for (AccountDo accountDo : list) {
                    if (StringUtils.equals(accountDo.getState(), AVAILABLE_ACCOUNT_STATE)) {
                        AccountDo account = AccountDo.builder()
                                .userName(accountDo.getUserName())
                                .uid(accountDo.getUid())
                                .headUrl(accountDo.getHeadUrl())
                                .thumbnailUrl(accountDo.getThumbnailUrl())
                                .displayName(accountDo.getDisplayName())
                                .type(accountDo.getType())
                                .email(accountDo.getEmail())
                                .freeze(accountDo.getFreeze())
                                .state(accountDo.getState())
                                .lastLoginTime(accountDo.getLastLoginTime())
                                .build();
                        resultList.add(account);
                    }
                }
                return resultList;
            }
        }
        return null;
    }

    /**
     * 通过用户名 类型 在职状态查询用户uid
     *
     * @param userName    用户名
     * @param accountType 账户类型
     * @param state       账户状态
     * @return java.lang.String
     * <AUTHOR>
     * @date 2020/4/229 11:15
     */
    @Override
    public String findUidByUserName(String userName, String accountType, String state) {
        Stopwatch startedStopwatch = Stopwatch.createStarted();
        List<AccountDo> accountDos = accountCacheService.getListByField(AccountDo::getUserName,
            userName);
        log.info("accountCacheService.getListByField time {}",
            startedStopwatch.elapsed(TimeUnit.MILLISECONDS));
        Stopwatch startedStopwatch2 = Stopwatch.createStarted();
        if (CollectionUtils.isNotEmpty(accountDos)) {
            Set<String> typeSet = null;
            if (StringUtils.isNotBlank(accountType)) {
                typeSet = new HashSet<>();
                String[] types = accountType.split(",");
                for (String type : types) {
                    typeSet.add(type.trim());
                }
            }
            Set<String> finalTypeSet = typeSet;
            List<AccountDo> dos = accountDos.stream()
                    .filter(a -> {
                        if (CollectionUtils.isNotEmpty(finalTypeSet) && !finalTypeSet.contains(a.getType())) {
                            return false;
                        }
                        if (StringUtils.isNotBlank(state)) {
                            return StringUtils.equals(a.getState(), state);
                        }
                        return true;
                    })
                    .sorted(Comparator.comparing(AccountDo::getUpdateAt).reversed())
                    .limit(1)
                    .collect(Collectors.toList());

            log.info("ApiAccountServiceImpl.findUidByUserName other time {}",startedStopwatch2.elapsed(TimeUnit.MILLISECONDS));
            if (CollectionUtils.isNotEmpty(dos)) {
                return dos.get(0).getUid();
            }
        }
        return null;
    }

    /**
     * 通过邮箱查询 uid
     *
     * @param email
     * @return java.lang.String
     * <AUTHOR>
     * @date 2020/4/229 11:15
     */
    @Override
    public String findUidByEmail(String email) {
        List<AccountDo> accountDos = accountCacheService.getListByField(AccountDo::getEmail, email);
        if (CollectionUtils.isNotEmpty(accountDos)) {
            List<AccountDo> collect = accountDos.stream()
                    .filter(accountDo -> StringUtils.equals(accountDo.getState(), AVAILABLE_ACCOUNT_STATE))
                    .sorted(Comparator.comparing(AccountDo::getUpdateAt).reversed())
                    .limit(1).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                return collect.get(0).getUid();
            }
        }

        return null;
    }

    @Override
    public List<AccountDo> fuzzyFindAccountListByUserName(String userName, Integer limit) {
        if (limit == null) {
            limit = 50;
        }
        if (limit > 200 || limit < 1) {
            limit = 200;
        }
        LambdaQueryWrapper<AccountDo> queryWrapper = Wrappers.<AccountDo>lambdaQuery()
                .eq(AccountDo::getState, AVAILABLE_ACCOUNT_STATE)
                .likeRight(AccountDo::getUserName, userName)
                .last(" limit " + limit);
        List<AccountDo> list = accountService.list(queryWrapper);
        if (!list.isEmpty()) {
            //只提供必要字段，其余置空
            return list.stream().map(a -> setHeadUrl(a)).collect(Collectors.toList());
        }
        return list;
    }


    /**
     * 通过uid list 查询账户
     *
     * @param uidList
     * @return java.util.List<cn.mioffice.uic.dao.entity.AccountDo>
     * <AUTHOR>
     * @date 2020/4/229 11:15
     */
    @Override
    public List<AccountDo> findAccountListByUids(List<String> uidList) {
        if (CollectionUtils.isNotEmpty(uidList)) {
            return accountCacheService.listAccountByUids(uidList);
        }
        return null;
    }

    @Override
    public List<String> findAvatarListByUids(List<String> uidList) {
        if (CollectionUtils.isNotEmpty(uidList)) {
            return accountCacheService.listAccountByUids(uidList).parallelStream().map(AccountDo::getThumbnailUrl).collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public PageDto<AccountDo> findAccountListByDateAndType(List<String> type, String date, Integer currentPage, Integer pageSize) {
        LambdaQueryWrapper<AccountDo> queryWrapper = Wrappers.<AccountDo>lambdaQuery().eq(AccountDo::getState, AVAILABLE_ACCOUNT_STATE);
        if (CollectionUtils.isNotEmpty(type)) {
            queryWrapper = queryWrapper.in(AccountDo::getType, type);
        }
        if (StringUtils.isNotBlank(date)) {
            String timeFormat = "yyyy-MM-dd";
            Assert.timeFormat(date, timeFormat, "时间格式错误");
            queryWrapper = queryWrapper.ge(AccountDo::getUpdateAt, date);
        }
        Page<AccountDo> page = new Page<>(currentPage, pageSize);
        Page<AccountDo> accountDoPage = this.accountService.page(page, queryWrapper);
        List<AccountDo> records = accountDoPage.getRecords();
        List<AccountDo> list = records.stream().map(a -> setHeadUrl(a)).collect(Collectors.toList());
        PageDto<AccountDo> resultPage = new PageDto<>();
        resultPage.setRecords(list).setCurrentPage(currentPage).setPageSize(pageSize).setTotal((int) accountDoPage.getTotal());
        return resultPage;
    }

    private AccountDo setHeadUrl(AccountDo account) {
        AccountDo accountDo = AccountDo.builder()
                .userName(account.getUserName())
                .uid(account.getUid())
                .headUrl("")
                .thumbnailUrl("")
                .displayName(account.getDisplayName())
                .type(account.getType())
                .email(account.getEmail())
                .freeze(account.getFreeze())
                .state(account.getState())
                .lastLoginTime(account.getLastLoginTime())
                .build();

        // 如果图片为空，返回空
        // 如果图片是path，返回 前缀+path
        // 如果图片是完整url，返回url
        if (StringUtils.isNotBlank(account.getHeadUrl())) {
            if (!account.getHeadUrl().startsWith("http")) {
                accountDo.setHeadUrl(avatarUrlPrefix + account.getHeadUrl());
            } else {
                accountDo.setHeadUrl(account.getHeadUrl());
            }
        }

        if (StringUtils.isNotBlank(account.getThumbnailUrl())) {
            if (!account.getThumbnailUrl().startsWith("http")) {
                accountDo.setThumbnailUrl(avatarUrlPrefix + account.getThumbnailUrl());
            } else {
                accountDo.setThumbnailUrl(account.getThumbnailUrl());
            }
        }

        return accountDo;
    }


    private OakbayAccountDto setAttribute(OakbayAccountDto dto) {

        //AI建议：这种写法性能最佳
      return OakbayAccountDto.builder()
            .id(dto.getId())
            .uid(StringUtils.isEmpty(dto.getUid()) ? null : dto.getUid())
            .personId(StringUtils.isEmpty(dto.getPersonId()) ? null : dto.getPersonId())
            .userName(StringUtils.isEmpty(dto.getUserName()) ? null : dto.getUserName())
            .email(StringUtils.isEmpty(dto.getEmail()) ? null : dto.getEmail())
            .name(StringUtils.isEmpty(dto.getName()) ? null : dto.getName())
            .displayName(StringUtils.isEmpty(dto.getDisplayName()) ? null : dto.getDisplayName())
            .type(StringUtils.isEmpty(dto.getType()) ? null : dto.getType())
            .sex(StringUtils.isEmpty(dto.getSex()) ? null : dto.getSex())
            .country(StringUtils.isEmpty(dto.getCountry()) ? null : dto.getCountry())
            .deptId(StringUtils.isEmpty(dto.getDeptId()) ? null : dto.getDeptId())
            .costCt(StringUtils.isEmpty(dto.getCostCt()) ? null : dto.getCostCt())
            .subordinate(StringUtils.isEmpty(dto.getSubordinate()) ? null : dto.getSubordinate())
            .officeCity(StringUtils.isEmpty(dto.getOfficeCity()) ? null : dto.getOfficeCity())
            .headUrl(StringUtils.isEmpty(dto.getHeadUrl()) ? null : dto.getHeadUrl())
            .thumbnailUrl(StringUtils.isEmpty(dto.getThumbnailUrl()) ? null : dto.getThumbnailUrl())
            .namePinyin(StringUtils.isEmpty(dto.getNamePinyin()) ? null : dto.getNamePinyin())
            .build();
    }

    @Override
    public PageDto<AccountDo> findAccountListByChannel(String channel, String date, Integer currentPage, Integer pageSize) {
        LambdaQueryWrapper<AccountDo> queryWrapper = Wrappers.<AccountDo>lambdaQuery();
        if (StringUtils.isNotEmpty(channel)) {
            queryWrapper = queryWrapper.eq(AccountDo::getOperateChannel, channel);
        }
        if (StringUtils.isNotBlank(date)) {
            String timeFormat = "yyyy-MM-dd";
            Assert.timeFormat(date, timeFormat, "时间格式错误");
            queryWrapper = queryWrapper.ge(AccountDo::getUpdateAt, date);
        }
        Page<AccountDo> page = new Page<>(currentPage, pageSize);
        Page<AccountDo> accountDoPage = this.accountService.page(page, queryWrapper);
        List<AccountDo> records = accountDoPage.getRecords();
        List<AccountDo> list = records.stream().map(a -> AccountDo.builder()
                .userName(a.getUserName())
                .uid(a.getUid())
                .displayName(a.getDisplayName())
                .type(a.getType())
                .email(a.getEmail())
                .freeze(a.getFreeze())
                .state(a.getState())
                .lastLoginTime(a.getLastLoginTime())
                .build()).collect(Collectors.toList());
        PageDto<AccountDo> resultPage = new PageDto<>();
        resultPage.setRecords(list).setCurrentPage(currentPage).setPageSize(pageSize).setTotal((int) accountDoPage.getTotal());
        return resultPage;
    }

    @Override
    public PageDto<AccountDo> findAccountListByPage(Integer currentPage, Integer pageSize) {
        Page<UserDo> page = new Page<>();
        page.setCurrent(currentPage).setSize(pageSize);
        List<AccountDo> list = accountCacheService.listActiveAccount(currentPage, pageSize);
        int totalSize = accountCacheService.getTotalSize();
        List<AccountDo> records = null;
        if (CollectionUtils.isNotEmpty(list)) {
            records = list.parallelStream().map(a -> AccountDo.builder().displayName(a.getDisplayName()).email(a.getEmail()).mobile(AesUtils.decrypt(a.getMobile(), aesProperties.getUicKey())).type(a.getType()).uid(a.getUid()).zoneCode(a.getZoneCode()).build()).collect(Collectors.toList());
        }
        PageDto<AccountDo> retPage = new PageDto<>();
        retPage.setCurrentPage(currentPage);
        retPage.setPageSize(pageSize);
        retPage.setTotal(totalSize);
        retPage.setRecords(records);
        return retPage;
    }

    @Override
    public List<OakbayAccountDto> fuzzySearchUserListByName(String name, Integer limit) {
        if (limit == null || limit > 30 || limit < 1) {
            limit = 30;
        }

        log.info("当前配置---enableES:{},enableOakbay:{},returnData:{}", enableES,enableOakbay,returnData);

        //迁移期间做的配置
        List<OakbayAccountDto> oakbayAccountDtoList = new ArrayList<>();
        List<OakbayAccountDto> esAccountDtoList = new ArrayList<>();

        if (enableOakbay) {
            oakbayAccountDtoList = oakbayUserService.fuzzyQueryByNames(name, limit);
        }

        if (enableES) {
            esAccountDtoList = esUserService.fuzzyQueryByNames(name, null, limit);
            esAccountDtoList = esAccountDtoList.stream().map(this::setAttribute).collect(Collectors.toList());
        }

        if ("es".equals(returnData)) {
            return esAccountDtoList;
        } else {
            return oakbayAccountDtoList;
        }
    }

    @Override
    public List<OakbayAccountDto> fuzzySearchUserListByNameV2(String name, Integer limit) {
        if (limit == null || limit > 30 || limit < 1) {
            limit = 30;
        }
        List<OakbayAccountDto> dtoList = esUserService.fuzzyQueryByNames(name, null,
            limit);
        return dtoList.stream().map(this::setAttribute).collect(Collectors.toList());
    }

    @Override
    public List<OakbayAccountDto> fuzzySearchAccountsByNameAndAccount(String name, String account,
        Integer limit) {
        if (limit == null || limit > 30 || limit < 1) {
            limit = 30;
        }
        UserDo user = userCacheService.getByUserName(account);
        List<OakbayAccountDto> oakbayAccountList;
        List<String> userDeptPath = null;
        if (ObjectUtils.isNotNull(user) && StringUtils.isNotEmpty(user.getFullDeptDescr())) {
            // 获取当前用户的多级部门优先级列表
            Map<String, String> deptMap = departmentService.genFullDeptId(user.getFullDeptDescr());
            if (MapUtils.isNotEmpty(deptMap)) {
                // 将部门ID按照层级排序（从叶子节点到根节点）
                userDeptPath = new ArrayList<>();

                try {
                    // 解析fullDeptDescr JSON字符串，提取部门层级信息
                    List<Map<String, Object>> deptInfoList = JsonUtil.readValue(user.getFullDeptDescr(), List.class);
                    if (CollectionUtils.isNotEmpty(deptInfoList)) {
                        // 按level排序（从小到大，即从根节点到叶子节点）
                        deptInfoList.sort((a, b) -> {
                            String levelA = String.valueOf(a.get("level"));
                            String levelB = String.valueOf(b.get("level"));
                            return Integer.compare(Integer.parseInt(levelA), Integer.parseInt(levelB));
                        });

                        // 提取部门ID列表
                        userDeptPath = deptInfoList.stream()
                            .map(map -> String.valueOf(map.get("deptId")))
                            .collect(Collectors.toList());

                        // 反转列表，使其从叶子节点到根节点排序（与搜索时的优先级匹配）
                        Collections.reverse(userDeptPath);

                        log.debug("User department path (leaf to root): {}", userDeptPath);
                    }
                } catch (Exception e) {
                    log.error("Failed to process department path for user: {}", user.getUserName(), e);
                    // 如果解析失败，回退到原来的方式
                    userDeptPath = new ArrayList<>(deptMap.values());
                }
            }
        }
        oakbayAccountList = esUserService.fuzzyQueryByNames(name, userDeptPath, limit);
        return oakbayAccountList.stream().map(this::setAttribute).collect(Collectors.toList());
    }

    @Override
    public PageDto<AccountDo> findBaseAccountListByPage(Integer currentPage, Integer pageSize) {
        Page<UserDo> page = new Page<>();
        page.setCurrent(currentPage).setSize(pageSize);
        List<AccountDo> list = accountCacheService.listActiveAccount(currentPage, pageSize);
        int totalSize = accountCacheService.getTotalSize();
        List<AccountDo> records = null;
        if (CollectionUtils.isNotEmpty(list)) {
            records = list.parallelStream().map(a -> AccountDo.builder().userName(a.getUserName()).displayName(a.getDisplayName()).email(a.getEmail()).miliao(a.getMiliao()).type(a.getType()).uid(a.getUid()).id(a.getId()).build()).collect(Collectors.toList());
        }
        PageDto<AccountDo> retPage = new PageDto<>();
        retPage.setCurrentPage(currentPage);
        retPage.setPageSize(pageSize);
        retPage.setTotal(totalSize);
        retPage.setRecords(records);
        return retPage;
    }

    @Override
    public AccountOwnerDto findAccountOwnerByUserName(String userName) {

        AccountInfoDto account = accountRemote.getAccount(userName);

        if (null == account) {
            return null;
        }

        String accountOwner = StringUtils.isNotBlank(account.getAccountOwner()) ? account.getAccountOwner() : userName;

        return AccountOwnerDto.builder().accountOwner(accountOwner).build();
    }

}