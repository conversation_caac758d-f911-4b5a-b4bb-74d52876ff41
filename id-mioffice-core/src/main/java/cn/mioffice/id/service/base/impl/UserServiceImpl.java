package cn.mioffice.id.service.base.impl;

import cn.mioffice.id.common.config.db.RoutingDataSourceAnnotation;
import cn.mioffice.id.common.constants.DataBaseType;
import cn.mioffice.id.dao.entity.UserDo;
import cn.mioffice.id.dao.enums.UserHrStatus;
import cn.mioffice.id.dao.enums.UserType;
import cn.mioffice.id.dao.mapper.UserMapper;
import cn.mioffice.id.dto.input.QueryDto;
import cn.mioffice.id.service.base.UserService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.CaseFormat;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <p>
 * 用户主表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-27
 */
@Slf4j
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, UserDo> implements UserService {

    @Autowired
    private UserMapper mapper;

    @Override
    @RoutingDataSourceAnnotation(DataBaseType.UIC_READ_DB)
    public List<UserDo> queryByPersonIds(Collection<String> personIds) {
        return baseMapper.selectList(Wrappers.<UserDo>lambdaQuery().in(UserDo::getPersonId, personIds));
    }

    @Override
    @RoutingDataSourceAnnotation(DataBaseType.UIC_READ_DB)
    public List<UserDo> queryByUids(Collection<String> uids) {
        return baseMapper.selectList(Wrappers.<UserDo>lambdaQuery().in(UserDo::getUid, uids));
    }

    /**
     * 功能描述: 分页查询在职的用户
     *
     * @param pageSize
     * @param currentPage
     * @Return: com.baomidou.mybatisplus.extension.plugins.pagination.Page<cn.mioffice.uic.dao.entity.UserDo>
     * @Author: zhushiyao
     * @Date: 2020/1/21 16:51
     */
    @Override
    @RoutingDataSourceAnnotation(DataBaseType.UIC_READ_DB)
    public Page<UserDo> queryHiredUserByPage(Integer pageSize, Integer currentPage) {
        LambdaQueryWrapper<UserDo> queryWrapper = Wrappers.<UserDo>lambdaQuery().eq(UserDo::getHrStatus, UserHrStatus.ACTIVE.getId());
        Page<UserDo> page = new Page<>();
        page.setCurrent(currentPage).setSize(pageSize);
        return mapper.selectPage(page, queryWrapper);
    }

    /**
     * 功能描述: 根据idCardMd5获取用户信息
     *
     * @param idNumberHash 证件号的散列值
     * @return UserDo 用户信息
     * <AUTHOR>
     * @date 2020/6/16 5:05 下午
     */
    @Override
    @RoutingDataSourceAnnotation(DataBaseType.UIC_READ_DB)
    public UserDo getUserByIdNumberHash(String idNumberHash) {
        if (StringUtils.isNotBlank(idNumberHash)) {
            LambdaQueryWrapper<UserDo> queryWrapper = Wrappers.<UserDo>lambdaQuery().eq(UserDo::getIdNumberHash, idNumberHash);
            List<UserDo> list = mapper.selectList(queryWrapper);
            if (CollectionUtils.isNotEmpty(list)) {
                if (list.size() > 1) {
                    List<UserDo> resultList = list.stream().filter(u -> StringUtils.equals(UserHrStatus.ACTIVE.getId(), u.getHrStatus())).sorted(Comparator.comparing(UserDo::getId).reversed()).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(resultList)) {
                        return resultList.get(0);
                    }
                } else {
                    return list.get(0);
                }
            }
        }
        return null;
    }

    @Override
    @RoutingDataSourceAnnotation(DataBaseType.UIC_READ_DB)
    public UserDo getUserByMiliao(String miliao) {
        if (StringUtils.isNotBlank(miliao)) {
            LambdaQueryWrapper<UserDo> queryWrapper = Wrappers.<UserDo>lambdaQuery().eq(UserDo::getMiliao, miliao).eq(UserDo::getHrStatus, UserHrStatus.ACTIVE.getId());
            return mapper.selectOne(queryWrapper);
        }
        return null;
    }

    @Override
    @RoutingDataSourceAnnotation(DataBaseType.UIC_READ_DB)
    public List<UserDo> listUserByIdNumberHash(Collection<String> idNumberHashs) {
        if (CollectionUtils.isNotEmpty(idNumberHashs)) {
            LambdaQueryWrapper<UserDo> queryWrapper = Wrappers.<UserDo>lambdaQuery().in(UserDo::getIdNumberHash, idNumberHashs);
            return mapper.selectList(queryWrapper);
        }
        return null;
    }

    @Override
    @RoutingDataSourceAnnotation(DataBaseType.UIC_READ_DB)
    public UserDo getUserByPersonId(String personId) {
        if (StringUtils.isNotBlank(personId)) {
            return mapper.selectOne(
                    Wrappers.<UserDo>lambdaQuery()
                            .eq(UserDo::getPersonId, personId)
                            .last("order by last_hire_date desc, create_time desc limit 1")
            );
        }
        return null;
    }


    /**
     * 功能描述: 通过用户名查询对应的用户
     *
     * @param userName
     * @Return: cn.mioffice.uic.dao.entity.UserDo
     * @Author: lupeng6
     * @Date: 2020/2/3 11:06
     */
    @Override
    @RoutingDataSourceAnnotation(DataBaseType.UIC_READ_DB)
    public UserDo getUserByUserName(String userName) {
        if (StringUtils.isBlank(userName)) {
            return null;
        }
        LambdaQueryWrapper<UserDo> queryWrapper = Wrappers.<UserDo>lambdaQuery().eq(UserDo::getUserName, userName);
        return mapper.selectOne(queryWrapper);
    }

    @Override
    @RoutingDataSourceAnnotation(DataBaseType.UIC_READ_DB)
    public UserDo getUserByUid(String uid) {
        if (StringUtils.isBlank(uid)) {
            return null;
        }
        LambdaQueryWrapper<UserDo> queryWrapper = Wrappers.<UserDo>lambdaQuery().eq(UserDo::getUid, uid);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    @RoutingDataSourceAnnotation(DataBaseType.UIC_READ_DB)
    public Page<UserDo> getHireUserByType(String type, Page<UserDo> page) {
        return getUserByTypeAndStatus(UserHrStatus.ACTIVE.getId(), type, page);
    }

    @Override
    @RoutingDataSourceAnnotation(DataBaseType.UIC_READ_DB)
    public Page<UserDo> getUserByTypeAndStatus(String status, String type, Page<UserDo> page) {
        LambdaQueryWrapper<UserDo> queryWrapper = Wrappers.<UserDo>lambdaQuery().eq(UserDo::getHrStatus, status).eq(UserDo::getType, type);
        return baseMapper.selectPage(page, queryWrapper);
    }

    @Override
    @RoutingDataSourceAnnotation(DataBaseType.UIC_READ_DB)
    public Page<UserDo> getUserByTypesAndStatus(String status, List<String> types, Page<UserDo> page) {
        LambdaQueryWrapper<UserDo> queryWrapper = Wrappers.<UserDo>lambdaQuery().eq(UserDo::getHrStatus, status);
        if (CollectionUtils.isNotEmpty(types)) {
            queryWrapper.in(UserDo::getType, types);
        }
        return baseMapper.selectPage(page, queryWrapper);
    }

    @Override
    @RoutingDataSourceAnnotation(DataBaseType.UIC_READ_DB)
    public Page<UserDo> getUserByPage(Page<UserDo> page) {
        LambdaQueryWrapper<UserDo> queryWrapper = Wrappers.<UserDo>lambdaQuery().eq(UserDo::getHrStatus, UserHrStatus.ACTIVE.getId());
        return baseMapper.selectPage(page, queryWrapper);
    }

    @Override
    @RoutingDataSourceAnnotation(DataBaseType.UIC_READ_DB)
    public Page<UserDo> getHireUsers(Page<UserDo> page) {
        LambdaQueryWrapper<UserDo> queryWrapper = Wrappers.<UserDo>lambdaQuery().eq(UserDo::getHrStatus, UserHrStatus.ACTIVE.getId());
        return baseMapper.selectPage(page, queryWrapper);
    }

    @Override
    @RoutingDataSourceAnnotation(DataBaseType.UIC_READ_DB)
    public Page<UserDo> getUsersMiliao(String hrStatus, Page<UserDo> page) {
        LambdaQueryWrapper<UserDo> queryWrapper = Wrappers.<UserDo>lambdaQuery().eq(UserDo::getHrStatus, hrStatus).ne(UserDo::getMiliao, "");
        return baseMapper.selectPage(page, queryWrapper);
    }

    @Override
    @RoutingDataSourceAnnotation(DataBaseType.UIC_READ_DB)
    public Page<UserDo> getUsersByQuery(QueryDto query, Page<UserDo> page) {
        Field[] fields = QueryDto.class.getDeclaredFields();
        QueryWrapper<UserDo> queryWrapper = new QueryWrapper<>();
        for (Field field : fields) {
            field.setAccessible(true);
            try {
                String value = (String) field.get(query);
                if (org.apache.commons.lang3.StringUtils.isBlank(value)) {
                    continue;
                }
                String column = CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, field.getName());
                if (org.apache.commons.lang3.StringUtils.equals(field.getName(), "deptId")) {
                    queryWrapper.likeRight(org.apache.commons.lang3.StringUtils.isNotBlank(value), column, value);
                    queryWrapper.orderBy(true, true, column);
                } else {
                    queryWrapper.eq(org.apache.commons.lang3.StringUtils.isNotBlank(value), column, value);
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }
        queryWrapper.eq("hr_status", UserHrStatus.ACTIVE.getId());
        return baseMapper.selectPage(page, queryWrapper);
    }

    @Override
    @RoutingDataSourceAnnotation(DataBaseType.UIC_READ_DB)
    public Page<UserDo> getUsersByHireDate(LocalDate startDate, LocalDate endDate, Page<UserDo> page) {
        List<String> types = Lists.newArrayList(
                UserType.EMPLOYEE.getId(),
                UserType.INTERN.getId(),
                UserType.VENDOR.getId(),
                UserType.PARTTIME.getId()
        );
        LambdaQueryWrapper<UserDo> queryWrapper = Wrappers.<UserDo>lambdaQuery()
                .eq(UserDo::getHrStatus, UserHrStatus.ACTIVE.getId())
                .ge(UserDo::getLastHireDate, startDate)
                .lt(UserDo::getLastHireDate, endDate)
                .in(UserDo::getType, types);
        return baseMapper.selectPage(page, queryWrapper);
    }

    @Override
    @RoutingDataSourceAnnotation(DataBaseType.UIC_READ_DB)
    public Page<UserDo> findUsersPageByUpdateTime(Collection<String> types, String hrStatus, LocalDateTime startDate, LocalDateTime endDate, Page<UserDo> page) {
        LambdaQueryWrapper<UserDo> queryWrapper = Wrappers.<UserDo>lambdaQuery()
                .ge(UserDo::getUpdateTime, startDate)
                .lt(UserDo::getUpdateTime, endDate);
        if (CollectionUtils.isNotEmpty(types)) {
            queryWrapper.in(UserDo::getType, types);
        }
        if (StringUtils.isNotBlank(hrStatus) && UserHrStatus.getHrStatusSet().contains(hrStatus)) {
            queryWrapper.eq(UserDo::getHrStatus, hrStatus);
        }
        return baseMapper.selectPage(page, queryWrapper);
    }


    /**
     * 通过userName 查询用户
     *
     * @param userNames
     * @return java.util.List<cn.mioffice.uic.dao.entity.UserDo>
     * <AUTHOR>
     * @date 2020/3/26 20:53
     */
    @Override
    @RoutingDataSourceAnnotation(DataBaseType.UIC_READ_DB)
    public List<UserDo> queryByUserNames(Collection<String> userNames) {
        if (CollectionUtils.isEmpty(userNames)) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<UserDo> queryWrapper = Wrappers.<UserDo>lambdaQuery().in(UserDo::getUserName, userNames);
        return list(queryWrapper);
    }

    @Override
    @RoutingDataSourceAnnotation(DataBaseType.UIC_READ_DB)
    public List<UserDo> queryByEmails(Collection<String> emails) {
        if (CollectionUtils.isEmpty(emails)) {
            return new ArrayList<>();
        }
        return list(Wrappers.<UserDo>lambdaQuery().in(UserDo::getEmail, emails));
    }

    /**
     * 通过部门id 查询用户
     *
     * @param deptId
     * @return java.util.List<cn.mioffice.uic.dao.entity.UserDo>
     * <AUTHOR>
     * @date 2020/4/13 20:27
     */
    @Override
    @RoutingDataSourceAnnotation(DataBaseType.UIC_READ_DB)
    public List<UserDo> queryByDeptId(String deptId) {
        LambdaQueryWrapper<UserDo> queryWrapper = Wrappers.<UserDo>lambdaQuery().likeRight(UserDo::getDeptId, deptId);
        return list(queryWrapper);
    }

    /**
     * 通过原始工号查询
     *
     * @param personIds
     * @return java.util.List<cn.mioffice.uic.dao.entity.UserDo>
     * <AUTHOR>
     * @date 2020/4/29 18:58
     */
    @Override
    @RoutingDataSourceAnnotation(DataBaseType.UIC_READ_DB)
    public List<UserDo> queryByOriginalPersonIds(Set<String> personIds) {
        LambdaQueryWrapper<UserDo> queryWrapper = Wrappers.<UserDo>lambdaQuery().in(UserDo::getOriginalPersonId, personIds);
        return list(queryWrapper);
    }

    @Override
    public List<String> getAllMiliao() {
        LambdaQueryWrapper<UserDo> queryWrapper = Wrappers.<UserDo>lambdaQuery().eq(UserDo::getHrStatus, "A").ne(UserDo::getMiliao, "").select(UserDo::getMiliao);
        return list(queryWrapper).stream().map(UserDo::getMiliao).collect(Collectors.toList());
    }

    /**
     * 查询已到离职时间但依然在职的员工
     *
     * @param terminateTime 离职时间
     * @return
     * <AUTHOR>
     * @date 2020/7/3 3:19 下午
     */
    @Override
    @RoutingDataSourceAnnotation(DataBaseType.UIC_READ_DB)
    public List<UserDo> queryUnemployed(String terminateTime) {
        if (StringUtils.isNotBlank(terminateTime)) {
            return mapper.queryUnemployed(terminateTime);
        }
        return Lists.emptyList();
    }
    @Override
    @RoutingDataSourceAnnotation(DataBaseType.UIC_READ_DB)
    public List<UserDo> queryUnConfirmEntry(String confirmEntryTime) {
        if (StringUtils.isNotBlank(confirmEntryTime)) {
            return mapper.queryUnConfirmEntry(confirmEntryTime);
        }
        return Lists.emptyList();
    }
    @Override
    public void updateMobileByUserName(String userName, String zoneCode, String mobile) {

        LambdaUpdateWrapper<UserDo> userUpdateWrapper = Wrappers.<UserDo>lambdaUpdate()
                .set(UserDo::getMobile, mobile)
                .set(UserDo::getZoneCode, zoneCode)
                .eq(UserDo::getUserName, userName);
        this.update(userUpdateWrapper);
    }

    @Override
    public void updateUserNameByUid(String uid, String userName) {
        if (StringUtils.isBlank(userName)) {
            return;
        }
        LambdaUpdateWrapper<UserDo> userUpdateWrapper = Wrappers.<UserDo>lambdaUpdate()
                .set(UserDo::getUserName, userName)
                .eq(UserDo::getUid, uid);
        this.update(userUpdateWrapper);
    }

    @Override
    public void updateMailByUid(String uid, String mail) {
        if (StringUtils.isBlank(mail)) {
            return;
        }
        LambdaUpdateWrapper<UserDo> userUpdateWrapper = Wrappers.<UserDo>lambdaUpdate()
                .set(UserDo::getEmail, mail)
                .eq(UserDo::getUid, uid);
        this.update(userUpdateWrapper);
    }

    @Override
    @RoutingDataSourceAnnotation(DataBaseType.UIC_READ_DB)
    public List<String> queryMultiIdNumberHash() {
        return mapper.queryMultiIdNumberHash();
    }

    @Override
    @RoutingDataSourceAnnotation(DataBaseType.UIC_READ_DB)
    public List<UserDo> queryNullUserName() {
        return mapper.queryNullUserName();
    }

    @Override
    @RoutingDataSourceAnnotation(DataBaseType.UIC_READ_DB)
    public List<UserDo> findUserByPositionCode(String positionCode) {
        if (StringUtils.isBlank(positionCode)) {
            return null;
        }
        LambdaQueryWrapper<UserDo> userWrapper = Wrappers.<UserDo>lambdaQuery()
                .eq(UserDo::getPositionCode, positionCode);
        return mapper.selectList(userWrapper);
    }

    /**
     * 通用分页查询方法
     * @param pageSize 每页大小
     * @param lastId 上一页最后一条记录的ID
     * @param queryWrapper 查询条件
     * @param mapper 结果转换函数
     * @return 查询结果
     * @throws RuntimeException 当查询次数超过最大限制时抛出
     */
    private <T> List<T> queryByPage(int pageSize, Long lastId, LambdaQueryWrapper<UserDo> queryWrapper, Function<UserDo, T> mapper) {
        List<T> result = new ArrayList<>();
        boolean hasMore = true;
        int queryCount = 0;
        final int MAX_QUERY_COUNT = 2000;

        while (hasMore) {
            queryCount++;
            if (queryCount > MAX_QUERY_COUNT) {
                log.error("查询次数超过最大限制: {}, 当前查询SQL: {}, 当前查询页数: {}", MAX_QUERY_COUNT, queryWrapper.getSqlSegment(), queryCount);
                throw new RuntimeException("查询次数超过最大限制: " + MAX_QUERY_COUNT);
            }

            // 添加ID条件
            if (lastId != null) {
                queryWrapper.gt(UserDo::getId, lastId);
            }
            // 限制每页大小
            queryWrapper.last("limit " + pageSize);

            try {
                log.info("开始查询第{}页数据, 每页大小: {}, 上一页最后ID: {}", queryCount, pageSize, lastId);
                List<UserDo> records = this.mapper.selectList(queryWrapper);
                if (CollectionUtils.isNotEmpty(records)) {
                    List<T> mappedRecords = records.stream()
                            .map(mapper)
                            .collect(Collectors.toList());
                    result.addAll(mappedRecords);

                    // 更新lastId为当前页最后一条记录的ID
                    lastId = records.get(records.size() - 1).getId();
                    hasMore = records.size() == pageSize;
                    log.info("第{}页查询完成, 获取到{}条数据", queryCount, records.size());
                } else {
                    hasMore = false;
                    log.info("第{}页查询完成, 无数据", queryCount);
                }
            } catch (Exception e) {
                log.error("分页查询异常, 查询SQL: {}, 当前查询页数: {}, 异常信息: ", queryWrapper.getSqlSegment(), queryCount, e);
                throw new RuntimeException("分页查询异常", e);
            }
        }
        return result;
    }

    @Override
    public List<Long> listActiveId() {
        try {
            LambdaQueryWrapper<UserDo> userWrapper = Wrappers.<UserDo>lambdaQuery().select(UserDo::getId)
                    .eq(UserDo::getHrStatus, UserHrStatus.ACTIVE.getId());
            List<Long> list = queryByPage(5000, null, userWrapper, UserDo::getId);
            if (CollectionUtils.isNotEmpty(list)) {
                return list;
            }
            return null;
        } catch (Exception e) {
            log.error("查询活跃用户ID列表异常", e);
            return null;
        }
    }

    @Override
    public List<String> listActiveMiliao() {
        try {
            LambdaQueryWrapper<UserDo> userWrapper = Wrappers.<UserDo>lambdaQuery().select(UserDo::getId,UserDo::getMiliao)
                    .eq(UserDo::getHrStatus, UserHrStatus.ACTIVE.getId()).ne(UserDo::getMiliao, "");
            List<String> list = queryByPage(5000, null, userWrapper, UserDo::getMiliao);
            if (CollectionUtils.isNotEmpty(list)) {
                return list;
            }
            return null;
        } catch (Exception e) {
            log.error("查询活跃用户米聊号列表异常", e);
            return null;
        }
    }

    @Override
    @RoutingDataSourceAnnotation(DataBaseType.UIC_READ_DB)
    public List<UserDo> listAllActiveUser() {
        try {
            LambdaQueryWrapper<UserDo> userWrapper = Wrappers.<UserDo>lambdaQuery().eq(UserDo::getHrStatus, UserHrStatus.ACTIVE.getId());
            return queryByPage(5000, null, userWrapper, Function.identity());
        } catch (Exception e) {
            log.error("查询所有活跃用户异常", e);
            return null;
        }
    }

    @Override
    @RoutingDataSourceAnnotation(DataBaseType.UIC_READ_DB)
    public Page<UserDo> listPreEntryUserByCreateTime(LocalDateTime startTime, LocalDateTime endTime, Page<UserDo> page) {
        LambdaQueryWrapper<UserDo> queryWrapper = Wrappers.<UserDo>lambdaQuery()
                .select(UserDo::getUserName, UserDo::getPersonId, UserDo::getName, UserDo::getEmail, UserDo::getDeptId, UserDo::getDeptDescr, UserDo::getFullDeptDescr, UserDo::getLastHireDate, UserDo::getType, UserDo::getOfficeCity)
                .eq(UserDo::getHrStatus, UserHrStatus.PRE_ENTRY.getId())
                .in(UserDo::getType, UserType.getPsUserTypes())
                .ge(UserDo::getCreateTime, startTime)
                .lt(UserDo::getCreateTime, endTime);
        return baseMapper.selectPage(page, queryWrapper);
    }
}
