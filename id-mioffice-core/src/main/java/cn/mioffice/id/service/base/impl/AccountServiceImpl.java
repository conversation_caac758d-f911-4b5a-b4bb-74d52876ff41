package cn.mioffice.id.service.base.impl;

import cn.mioffice.id.common.config.db.RoutingDataSourceAnnotation;
import cn.mioffice.id.common.config.properties.AesProperties;
import cn.mioffice.id.common.constants.DataBaseType;
import cn.mioffice.id.common.util.AesUtils;
import cn.mioffice.id.dao.entity.AccountDo;
import cn.mioffice.id.dao.enums.OperateChannel;
import cn.mioffice.id.dao.mapper.AccountMapper;
import cn.mioffice.id.dto.output.es.ESAccountDto;
import cn.mioffice.id.dto.output.oakbay.OakbayAccountDto;
import cn.mioffice.id.service.base.AccountService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.text.SimpleDateFormat;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static cn.mioffice.id.common.constants.CommonCst.AVAILABLE_ACCOUNT_STATE;

/**
 * @version 1.0
 * @className AccountServiceImpl
 * @description:
 * @author: Perry_Zhu
 * @date: 2020/3/5 下午5:25
 **/
@Service
@Slf4j
public class AccountServiceImpl extends ServiceImpl<AccountMapper, AccountDo> implements AccountService {
    @Autowired
    private AesProperties aesProperties;
    @Autowired
    private AccountMapper mapper;

    @Override
    @RoutingDataSourceAnnotation(DataBaseType.UIC_READ_DB)
    public List<AccountDo> queryByUserNames(Collection<String> userNames) {
        List<AccountDo> ret = null;
        if (CollectionUtils.isNotEmpty(userNames)) {
            LambdaQueryWrapper<AccountDo> queryWrapper = Wrappers.<AccountDo>lambdaQuery().in(AccountDo::getUserName, userNames);
            ret = super.list(queryWrapper);
        }
        return ret;
    }

    /**
     * 通过邮箱查询账号
     *
     * @param emails
     * @return java.util.List<cn.mioffice.uic.dao.entity.AccountDo>
     * <AUTHOR>
     * @date 2020/3/26 16:59
     */
    @Override
    @RoutingDataSourceAnnotation(DataBaseType.UIC_READ_DB)
    public List<AccountDo> queryByEmails(Set<String> emails) {
        List<AccountDo> ret = null;
        if (CollectionUtils.isNotEmpty(emails)) {
            LambdaQueryWrapper<AccountDo> queryWrapper = Wrappers.<AccountDo>lambdaQuery().in(AccountDo::getEmail, emails);
            ret = this.list(queryWrapper);
        }
        return ret;
    }

    @Override
    @RoutingDataSourceAnnotation(DataBaseType.UIC_READ_DB)
    public AccountDo getAccountByUserName(String userName) {
        if (StringUtils.isNotBlank(userName)) {
            LambdaQueryWrapper<AccountDo> queryWrapper = Wrappers.<AccountDo>lambdaQuery().eq(AccountDo::getUserName, userName);
            return this.baseMapper.selectOne(queryWrapper);
        }
        return null;
    }

    @Override
    @RoutingDataSourceAnnotation(DataBaseType.UIC_READ_DB)
    public AccountDo getAccountByMiliao(String miliao) {
        if (StringUtils.isNotBlank(miliao)) {
            LambdaQueryWrapper<AccountDo> queryWrapper = Wrappers.<AccountDo>lambdaQuery().eq(AccountDo::getMiliao, miliao).last("limit 1");
            return this.baseMapper.selectOne(queryWrapper);
        }
        return null;
    }

    @Override
    @RoutingDataSourceAnnotation(DataBaseType.UIC_READ_DB)
    public AccountDo getAccountByUid(String uid) {
        if (StringUtils.isNotBlank(uid)) {
            LambdaQueryWrapper<AccountDo> queryWrapper = Wrappers.<AccountDo>lambdaQuery().eq(AccountDo::getUid, uid);
            return this.baseMapper.selectOne(queryWrapper);
        }
        return null;
    }

    @Override
    @RoutingDataSourceAnnotation(DataBaseType.UIC_READ_DB)
    public List<AccountDo> listAvailableAccountByMobile(String mobile) {
        if (StringUtils.isNotBlank(mobile)) {
            LambdaQueryWrapper<AccountDo> queryWrapper = Wrappers.<AccountDo>lambdaQuery().eq(AccountDo::getState, AVAILABLE_ACCOUNT_STATE).eq(AccountDo::getMobile, AesUtils.encrypt(mobile, aesProperties.getUicKey()));
            return list(queryWrapper);
        }
        return null;
    }

    @Override
    public void updateMobileByUserName(String userName, String zoneCode, String mobile) {
        LambdaUpdateWrapper<AccountDo> userUpdateWrapper = Wrappers.<AccountDo>lambdaUpdate()
                .set(AccountDo::getMobile, mobile)
                .set(AccountDo::getZoneCode, zoneCode)
                .eq(AccountDo::getUserName, userName);
        this.update(userUpdateWrapper);
    }

    @Override
    public void updateOperateChannel(String operateChannel, List<String> uidList) {
        LambdaQueryWrapper<AccountDo> queryWrapper = Wrappers.<AccountDo>lambdaQuery().in(AccountDo::getUid, uidList);
        List<AccountDo> accounts = this.list(queryWrapper);
        if (accounts == null) {
            return;
        }
        List<String> uids = accounts.stream().filter(a -> StringUtils.isNotBlank(a.getOperateChannel())).map(AccountDo::getUid).collect(Collectors.toList());
        if (uids.size() == 0 || !OperateChannel.isOperateChannel(operateChannel)) {
            return;
        }
        LambdaUpdateWrapper<AccountDo> userUpdateWrapper = Wrappers.<AccountDo>lambdaUpdate()
                .set(AccountDo::getOperateChannel, operateChannel)
                .in(AccountDo::getUid, uids);
        this.update(userUpdateWrapper);
    }

    @Override
    public List<String> getAllMiliao() {
        LambdaQueryWrapper<AccountDo> queryWrapper = Wrappers.<AccountDo>lambdaQuery().eq(AccountDo::getState, "0").ne(AccountDo::getMiliao, "").select(AccountDo::getMiliao);
        return list(queryWrapper).stream().map(AccountDo::getMiliao).collect(Collectors.toList());
    }

    @Override
    @RoutingDataSourceAnnotation(DataBaseType.UIC_READ_DB)
    public List<AccountDo> queryByUserNames(List<String> userNames) {
        if (userNames != null) {
            LambdaQueryWrapper<AccountDo> queryWrapper = Wrappers.<AccountDo>lambdaQuery().in(AccountDo::getUserName, userNames);
            return this.baseMapper.selectList(queryWrapper);
        }
        return null;
    }

    /**
     * 通用分页处理方法
     * @param pageQuery 分页查询函数
     * @param <T> 返回数据类型
     * @return 查询结果列表
     */
    private <T> List<T> pageQuery(PageQueryFunction<T> pageQuery) {
        List<T> result = new ArrayList<>();
        int pageSize = 5000;
        long startTime = System.currentTimeMillis();
        int maxQueryTimes = 2000; // 最大查询次数限制
        int queryTimes = 0;
        
        // 获取起始ID
        Long lastId = 0L;
        log.info("开始分页查询，每页大小: {}", pageSize);
        
        while (true) {
            queryTimes++;
            if (queryTimes > maxQueryTimes) {
                String errMsg = String.format("查询次数超过限制，已处理数据量: %d, 已查询次数: %d, 总耗时: %d秒",
                    result.size(),
                    queryTimes - 1,
                    (System.currentTimeMillis() - startTime) / 1000);

                log.error(errMsg);
                throw new RuntimeException(errMsg);
            }
            
            List<T> pageData = pageQuery.query(lastId, pageSize);
            if (CollectionUtils.isEmpty(pageData)) {
                break;
            }
            result.addAll(pageData);
            
            // 获取当前页最后一条数据的ID
            if (!pageData.isEmpty()) {
                lastId = getLastId(pageData);
            }
            
            // 记录进度日志
            long currentTime = System.currentTimeMillis();
            long costTime = (currentTime - startTime) / 1000;
            log.info("分页查询进度: 第{}次查询, 已处理: {}条数据, 已耗时: {}秒",
                queryTimes, 
                result.size(), 
                costTime);
            
            if (pageData.size() < pageSize) {
                break;
            }
        }
        
        long totalCostTime = (System.currentTimeMillis() - startTime) / 1000;
        log.info("分页查询完成，实际处理数据量: {}, 总查询次数: {}, 总耗时: {}秒",
            result.size(), queryTimes, totalCostTime);
        
        return result;
    }

    /**
     * 获取分页数据的最后一条记录的ID
     */
    private <T> Long getLastId(List<T> pageData) {
        if (pageData.isEmpty()) {
            return 0L;
        }
        
        // 根据不同的返回类型获取ID
        Object last = pageData.get(pageData.size() - 1);
        Long lastId;
        if (last instanceof OakbayAccountDto) {
            lastId = ((OakbayAccountDto) last).getId();
        } else if (last instanceof ESAccountDto) {
            lastId = ((ESAccountDto) last).getId();
        } else if (last instanceof String) {
            lastId = Long.parseLong((String) last);
        } else {
            return 0L;
        }
        
        // 返回 lastId + 1，避免重复数据
        return lastId + 1;
    }

    @Override
    @RoutingDataSourceAnnotation(DataBaseType.UIC_READ_DB)
    public List<OakbayAccountDto> listAllEsAccount() {
        return pageQuery((lastId, limit) -> mapper.listAllEsAccount(lastId, limit));
    }

    @Override
    @RoutingDataSourceAnnotation(DataBaseType.UIC_READ_DB)
    public List<ESAccountDto> listAllEsAccountV2() {
        return pageQuery((lastId, limit) -> mapper.listAllEsAccountV2(lastId, limit));
    }

    @Override
    @RoutingDataSourceAnnotation(DataBaseType.UIC_READ_DB)
    public List<String> listAllRemoveIndex() {
        return pageQuery((lastId, limit) -> mapper.listAllRemoveIndex(lastId, limit));
    }

    /**
     * 分页查询函数接口
     */
    @FunctionalInterface
    private interface PageQueryFunction<T> {
        List<T> query(Long lastId, int limit);
    }

    /**
     * 通过userName 查询绑定手机号
     *
     * @param userName
     * <AUTHOR>
     * @date 2020/8/20 20:09
     */
    @Override
    @RoutingDataSourceAnnotation(DataBaseType.UIC_READ_DB)
    public String getMobileByUserName(String userName) {
        AccountDo accountDo = getOne(Wrappers.<AccountDo>lambdaQuery().eq(AccountDo::getUserName, userName));
        if (accountDo != null) {
            return accountDo.getMobile();
        }
        return null;
    }
}