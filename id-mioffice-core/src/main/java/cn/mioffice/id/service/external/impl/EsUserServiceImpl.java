package cn.mioffice.id.service.external.impl;

import cn.mioffice.id.common.util.JsonUtil;
import cn.mioffice.id.dto.output.es.ESAccountDto;
import cn.mioffice.id.dto.output.oakbay.OakbayAccountDto;
import cn.mioffice.id.service.external.EsUserService;
import com.google.common.collect.Lists;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.delete.DeleteResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.support.replication.ReplicationResponse.ShardInfo;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.lucene.search.function.CombineFunction;
import org.elasticsearch.common.lucene.search.function.FunctionScoreQuery.ScoreMode;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.QueryStringQueryBuilder;
import org.elasticsearch.index.query.functionscore.FunctionScoreQueryBuilder;
import org.elasticsearch.index.query.functionscore.FunctionScoreQueryBuilder.FilterFunctionBuilder;
import org.elasticsearch.index.query.functionscore.ScoreFunctionBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.elasticsearch.search.SearchHit;
import java.io.IOException;
import java.util.Comparator;


/**
 * EsUserServiceImpl
 */
@Service
@Slf4j
public class EsUserServiceImpl implements EsUserService {

  @Value("${es.index}")
  private String esIndex;
  private final RequestOptions COMMON_OPTIONS = RequestOptions.DEFAULT;
  private static final int SLIVER = 30;

  @Autowired
  private RestHighLevelClient highClient;

  @Override
  public List<OakbayAccountDto> fuzzyQueryByNames(String name, List<String> deptPriorityList, int limit) {
    if (StringUtils.isBlank(name) || 0 == limit) {
      return Collections.emptyList();
    }
    name = convertStr(name);
    SearchResponse searchResponse = search(name, deptPriorityList, limit);
    if (ObjectUtils.isNotEmpty(searchResponse)
        && ObjectUtils.isNotEmpty(searchResponse.getHits())
        && ObjectUtils.isNotEmpty(searchResponse.getHits().getHits())) {
      List<OakbayAccountDto> result = Arrays.stream(searchResponse.getHits().getHits()).map(
              hit -> JsonUtil.readValue(hit.getSourceAsString(), OakbayAccountDto.class))
          .collect(Collectors.toList());
      return result;
    } else {
      return Collections.emptyList();
    }
  }


  @Override
  public void batchPutUser(List<ESAccountDto> esAccountList) {
    if (CollectionUtils.isNotEmpty(esAccountList)) {
      // 处理每个账户的部门路径信息
      esAccountList.forEach(this::processDeptPath);

      List<List<ESAccountDto>> partition = Lists.partition(esAccountList, SLIVER);
      for (List<ESAccountDto> list : partition) {
        BulkRequest bulkRequest = new BulkRequest(esIndex);
        list.forEach(esAccountDto -> bulkRequest.add(
            new IndexRequest(esIndex)
                .source(JsonUtil.toJSon(esAccountDto), XContentType.JSON)
                .id(esAccountDto.getId().toString())));
        try {
          BulkResponse bulk = highClient.bulk(bulkRequest, COMMON_OPTIONS);
          log.info("put user to es success:{}", bulk);
        } catch (IOException e) {
          log.error("put user to es failed:{}", Arrays.toString(e.getStackTrace()));
          log.error("bulkRequest:{}", bulkRequest);
        }
      }
    }
  }


  @Override
  public void batchDeleteUser(List<String> esAccountIdList) {
    if (CollectionUtils.isNotEmpty(esAccountIdList)) {
      List<List<String>> partitionLists = Lists.partition(esAccountIdList, SLIVER);
      for (List<String> list : partitionLists) {
        BulkRequest bulkRequest = new BulkRequest(esIndex);
        list.forEach(id -> bulkRequest.add(
            new DeleteRequest(esIndex, id)));
        try {
          highClient.bulk(bulkRequest, COMMON_OPTIONS);
          log.info("delete user to es success:{}", list);
        } catch (IOException e) {
          log.debug("delete user from es failed:", e.fillInStackTrace());
        }
      }
    }
  }

  @Override
  public List<ESAccountDto> searchAll() {
    SearchRequest searchRequest = new SearchRequest(esIndex);
    SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

    searchSourceBuilder.query(QueryBuilders.matchAllQuery());
    searchRequest.source(searchSourceBuilder);
    SearchResponse searchResponse = null;
    List<ESAccountDto> esAccountDtoList = new ArrayList<>();
    try {
      searchResponse = highClient.search(searchRequest, COMMON_OPTIONS);
      if (handleSearchResponse(searchResponse)) {
        for (SearchHit hit : searchResponse.getHits().getHits()) {
          esAccountDtoList.add(JsonUtil.readValue(hit.getSourceAsString(),
              ESAccountDto.class));
        }
        log.info("search success:{}", esAccountDtoList);
      }
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
    return esAccountDtoList;
  }

  @Override
  public void putUser(ESAccountDto accountDto) {
    // 处理部门路径信息
    processDeptPath(accountDto);

    IndexRequest indexRequest = new IndexRequest(esIndex)
        .id(accountDto.getId().toString())
        .source(JsonUtil.toJSon(accountDto), XContentType.JSON);
    try {
      IndexResponse putResponse = highClient.index(indexRequest, COMMON_OPTIONS);
      if (handleResponse(putResponse.getShardInfo())) {
        log.info("put user to es success:{}", JsonUtil.toJSon(accountDto));
      }
    } catch (Exception e) {
      log.error("put user to es failed:", e.fillInStackTrace());
    }
  }

  @Override
  public void deleteUserByUserId(String id) {
    DeleteRequest deleteRequest = new DeleteRequest(esIndex, id);
    try {
      //3 通过deleteByQuery来发起删除请求
      DeleteResponse deleteResponse = highClient.delete(deleteRequest, RequestOptions.DEFAULT);
      if (handleResponse(deleteResponse.getShardInfo())) {
        log.info("deleteData:{},删除成功", id);
      }
    } catch (IOException e) {
      log.error("delete user from es failed:", e.fillInStackTrace());
    }


  }

  public boolean handleResponse(ShardInfo shardInfo) {
    if (shardInfo.getTotal() != shardInfo.getSuccessful()) {
      handleFailure(shardInfo.getFailures());
      return false;
    }
    return true;
  }

  public boolean handleSearchResponse(SearchResponse searchResponse) {
    if (searchResponse.getTotalShards() != searchResponse.getSuccessfulShards()) {
      handleFailure(searchResponse.getShardFailures());
      return false;
    }
    return true;
  }


  public <T> void handleFailure(T[] failures) {
    if (failures != null) {
      log.error("put user to es failed:{}", Arrays.toString(failures));
    } else {
      log.error("put user to es failed: no failures information");
    }
  }

  public SearchResponse search(String name, List<String> deptPriorityList, int limit) {
    SearchRequest searchRequest = new SearchRequest(esIndex);
    String baseQueryStr =
        "user_name:(" + name + "*) OR user_name:(*" + name + "*) OR display_name:(*" + name + "*)";
    QueryStringQueryBuilder baseQueryBuilder = QueryBuilders.queryStringQuery(baseQueryStr);
    FilterFunctionBuilder[] functions;
    FunctionScoreQueryBuilder finalQueryBuilder;
    if (deptPriorityList == null || deptPriorityList.isEmpty()) {
      functions = buildCommonFilter(4, name);
      finalQueryBuilder = buildDefaultQuery(searchRequest, functions, baseQueryBuilder);
    } else {
      // 如果有部门信息，使用优化的查询逻辑
      functions = buildCommonFilter(4 + deptPriorityList.size(), name);
      finalQueryBuilder = buildOptimizedQuery(searchRequest, functions, baseQueryBuilder, deptPriorityList);

      // 记录日志，便于调试
      log.debug("Searching with department priority list: {}", deptPriorityList);
    }

    // Create a search source builder with the query
    SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder()
        .query(finalQueryBuilder)
        .size(limit);

    // Add a secondary sort by id to ensure stable sorting when scores are the same
    searchSourceBuilder.sort("_score", org.elasticsearch.search.sort.SortOrder.DESC);
    searchSourceBuilder.sort("id", org.elasticsearch.search.sort.SortOrder.ASC);

    searchRequest.source(searchSourceBuilder);
    SearchResponse searchResponse = null;
    try {
      searchResponse = highClient.search(searchRequest, COMMON_OPTIONS);
      handleSearchResponse(searchResponse);
    } catch (IOException e) {
      log.error("fail to search", e.fillInStackTrace());
    }
    return searchResponse;
  }

  public FunctionScoreQueryBuilder buildDefaultQuery(SearchRequest searchRequest,
      FilterFunctionBuilder[] functions,
      QueryStringQueryBuilder queryBuilder) {

    return QueryBuilders.functionScoreQuery(queryBuilder, functions)
        .scoreMode(ScoreMode.SUM).boostMode(CombineFunction.SUM);
  }

  public FunctionScoreQueryBuilder buildOptimizedQuery(
      SearchRequest searchRequest,
      FilterFunctionBuilder[] functions,
      QueryStringQueryBuilder queryBuilder,
      List<String> deptPriorityList) {
    int base = 4;

    // 使用部门路径匹配而不是仅匹配叶子部门
    for (int i = 0; i < deptPriorityList.size(); i++) {
      String deptId = deptPriorityList.get(i);
      // 计算权重：叶子部门权重最高，父部门权重逐级降低
      // 第一个元素(i=0)是叶子部门，应该有最高权重
      float weight = deptPriorityList.size() - i; // 反转权重计算

      // 使用deptPath字段进行匹配，这是一个包含所有部门层级的数组字段
      // 调整权重，确保部门匹配不会压过用户名精确匹配
      functions[base + i] = new FilterFunctionBuilder(
          QueryBuilders.termQuery("deptPath", deptId),
          // 降低部门匹配权重，让用户名精确匹配有更高优先级
          ScoreFunctionBuilders.weightFactorFunction(weight * 15f)
      );
    }
    return QueryBuilders.functionScoreQuery(queryBuilder, functions)
        .scoreMode(ScoreMode.SUM).boostMode(CombineFunction.SUM);
  }

  public FilterFunctionBuilder[] buildCommonFilter(int size, String name){
    FilterFunctionBuilder[] functions = new FilterFunctionBuilder[size];
    // 根据用户名前缀给予不同权重，而不是前缀+关键词的组合
    // p- 前缀的用户（合作伙伴账户）
    functions[0] = new FunctionScoreQueryBuilder.FilterFunctionBuilder(
        QueryBuilders.prefixQuery("user_name", "p-"),
        ScoreFunctionBuilders.weightFactorFunction(8)
    );
    // v- 前缀的用户（虚拟账户）
    functions[1] = new FunctionScoreQueryBuilder.FilterFunctionBuilder(
        QueryBuilders.prefixQuery("user_name", "v-"),
        ScoreFunctionBuilders.weightFactorFunction(6)
    );
    // f- 前缀的用户（功能账户）
    functions[2] = new FunctionScoreQueryBuilder.FilterFunctionBuilder(
        QueryBuilders.prefixQuery("user_name", "f-"),
        ScoreFunctionBuilders.weightFactorFunction(4)
    );
    // 精确匹配用户名模式的权重（正常员工账户）
    functions[3] = new FunctionScoreQueryBuilder.FilterFunctionBuilder(
        QueryBuilders.regexpQuery("user_name", name + "[0-9]*"),
        ScoreFunctionBuilders.weightFactorFunction(200)
    );
    return functions;
  }

  public static String convertStr(String s) {
    StringBuilder sb = new StringBuilder();
    for (int i = 0; i < s.length(); i++) {
      char c = s.charAt(i);
      if (c == '\\' || c == '+' || c == '-' || c == '!' || c == '(' || c == ')' || c == ':'
          || c == '^' || c == '[' || c == ']' || c == '\"' || c == '{' || c == '}' || c == '~'
          || c == '*' || c == '?' || c == '|' || c == '&' || c == '/' || c == '<') {
        sb.append('\\');
      }
      sb.append(c);
    }
    return sb.toString();
  }

  /**
   * 处理账户的部门路径信息
   * 从fullDeptDescr中提取部门ID列表，并设置到deptPath字段
   *
   * @param accountDto ES账户数据对象
   */
  private void processDeptPath(ESAccountDto accountDto) {
    if (accountDto == null || StringUtils.isBlank(accountDto.getFullDeptDescr())) {
      return;
    }

    try {
      // 解析fullDeptDescr JSON字符串，提取部门ID列表
      List<Map<String, Object>> deptInfoList = JsonUtil.readValue(accountDto.getFullDeptDescr(), List.class);
      if (CollectionUtils.isNotEmpty(deptInfoList)) {
        // 按level排序（从小到大，即从根节点到叶子节点）
        deptInfoList.sort((a, b) -> {
          String levelA = String.valueOf(a.get("level"));
          String levelB = String.valueOf(b.get("level"));
          return Integer.compare(Integer.parseInt(levelA), Integer.parseInt(levelB));
        });

        // 提取部门ID列表
        List<String> deptIds = deptInfoList.stream()
            .map(map -> String.valueOf(map.get("deptId")))
            .collect(Collectors.toList());

        // 反转列表，使其从叶子节点到根节点排序（与搜索时的优先级匹配）
        Collections.reverse(deptIds);
        accountDto.setDeptPath(deptIds);
      }
    } catch (Exception e) {
      log.error("Failed to process department path for account: {}", accountDto.getUserName(), e);
    }
  }

}
