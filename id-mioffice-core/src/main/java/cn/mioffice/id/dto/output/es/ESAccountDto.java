package cn.mioffice.id.dto.output.es;

import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ToString
public class ESAccountDto implements Serializable {
    private Long id;
    private String uid;
    private String personId;
    private String userName;
    private String email;
    private String name;
    private String displayName;
    private String type;
    private String sex;
    private String country;
    private String deptId;
    // 部门路径ID列表，从叶子节点到根节点
    private List<String> deptPath;
    private String costCt;
    private String subordinate;
    private String officeCity;
    private String headUrl;
    private String thumbnailUrl;
    private String namePinyin;
    // 完整的部门描述JSON字符串
    private String fullDeptDescr;
}
