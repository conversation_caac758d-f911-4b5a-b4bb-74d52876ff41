<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.mioffice.id.dao.mapper.AccountMapper">
    <select id="listAllEsAccount" resultType="cn.mioffice.id.dto.output.oakbay.OakbayAccountDto">
        select a.id,a.uid,a.user_name userName,a.email,a.display_name displayName,a.type,
                u.name,u.person_id personId,u.office_city officeCity,u.country,u.subordinate,
                u.sex,u.dept_id deptId,u.cost_ct costCt, a.head_url headUrl, a.thumbnail_url thumbnailUrl, u.name_pinyin namePinyin
        from account a
        left join user u on a.user_name = u.user_name
        where a.state='0'
    </select>
    <select id="listAllRemoveIndex" resultType="java.lang.String">
        select id from account where state = 2
    </select>
    <select id="listAllEsAccountV2" resultType="cn.mioffice.id.dto.output.es.ESAccountDto">
        select a.id,a.uid,a.user_name userName,a.email,a.display_name displayName,a.type,
               u.name,u.person_id personId,u.office_city officeCity,u.country,u.subordinate,
               u.sex,u.dept_id deptId,u.cost_ct costCt, a.head_url headUrl, a.thumbnail_url thumbnailUrl, u.name_pinyin namePinyin,
               u.full_dept_descr fullDeptDescr
        from account a
                 left join user u on a.user_name = u.user_name
        where a.state='0'
    </select>
</mapper>
